'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>R<PERSON>, Heart, Users, BookOpen } from 'lucide-react';
import { BannerSlider } from '@/components/banner-slider';

export function HeroSection() {
  return (
    <BannerSlider>

      <div className="container mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Sacred symbol */}
          <div className="mb-8 flex justify-center">
            <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-2xl">
              <span className="text-3xl text-white font-bold">ॐ</span>
            </div>
          </div>

          {/* Main heading */}
          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight drop-shadow-2xl" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5)' }}>
            Sacred Community
          </h1>

          <p className="text-xl md:text-2xl text-white mb-4 font-medium drop-shadow-lg" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.8)' }}>
            Where Devotion Meets Connection
          </p>

          <p className="text-lg md:text-xl text-white/95 mb-12 max-w-3xl mx-auto leading-relaxed drop-shadow-lg" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}>
            Join our spiritual community to share prayers, discuss sacred texts, seek guidance,
            and connect with fellow devotees on your journey of faith and enlightenment.
          </p>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-6 w-6 text-orange-300 mr-2" />
                <span className="text-3xl font-bold text-white drop-shadow-lg">5,000+</span>
              </div>
              <p className="text-white/80">Devotees</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Heart className="h-6 w-6 text-red-300 mr-2" />
                <span className="text-3xl font-bold text-white drop-shadow-lg">10,000+</span>
              </div>
              <p className="text-white/80">Prayers Shared</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <BookOpen className="h-6 w-6 text-amber-300 mr-2" />
                <span className="text-3xl font-bold text-white drop-shadow-lg">500+</span>
              </div>
              <p className="text-white/80">Sacred Discussions</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
              Join Our Community
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-orange-500 text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-950 px-8 py-4 text-lg font-semibold">
              Explore Teachings
            </Button>
          </div>

          {/* Sacred quote */}
          <div className="mt-16 p-6 bg-black/30 backdrop-blur-sm rounded-2xl border border-white/20 max-w-2xl mx-auto">
            <p className="text-lg italic text-white mb-2 drop-shadow-lg" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
              "सर्वे भवन्तु सुखिनः सर्वे सन्तु निरामयाः"
            </p>
            <p className="text-sm text-white/90 drop-shadow-md" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}>
              May all beings be happy, may all beings be healthy
            </p>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
        <div className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </BannerSlider>
  );
}