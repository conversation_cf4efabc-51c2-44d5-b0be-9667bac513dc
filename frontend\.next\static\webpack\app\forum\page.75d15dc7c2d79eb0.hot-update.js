"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Search,Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* harmony import */ var _login_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    var _user_first_name_, _user_first_name;\n    _s();\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    const { user, logout } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto flex h-16 items-center px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"ॐ\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-xl bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                                        children: \"Sacred Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 mx-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search discussions...\",\n                                    className: \"pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                href: \"/forum\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"hidden sm:flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Forum Discussions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"destructive\",\n                                                className: \"absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"relative h-8 w-8 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                        className: \"h-8 w-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                src: user.avatar,\n                                                                alt: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                children: ((_user_first_name = user.first_name) === null || _user_first_name === void 0 ? void 0 : (_user_first_name_ = _user_first_name[0]) === null || _user_first_name_ === void 0 ? void 0 : _user_first_name_.toUpperCase()) || user.email[0].toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                                className: \"w-56\",\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-start gap-2 p-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col space-y-1 leading-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        user.first_name,\n                                                                        \" \",\n                                                                        user.last_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                    lineNumber: 101,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Profile\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                        onClick: logout,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Log out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>setShowLoginDialog(true),\n                                className: \"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_dialog__WEBPACK_IMPORTED_MODULE_9__.LoginDialog, {\n                open: showLoginDialog,\n                onOpenChange: setShowLoginDialog\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"mpCVeD3x/wH9BfbXT1O8l0ncBQE=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_7__.useTheme,\n        _auth_provider__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/header.tsx\n"));

/***/ })

});