'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { testConnection } from '@/lib/directus';

export default function TestDirectusPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleTest = async () => {
    setLoading(true);
    try {
      const testResult = await testConnection();
      setResult(testResult);
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Directus Connection Test</CardTitle>
          <CardDescription>
            Test the connection to your Directus instance and view available collections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={handleTest} disabled={loading}>
            {loading ? 'Testing...' : 'Test Connection'}
          </Button>

          {result && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">
                {result.success ? '✅ Connection Successful' : '❌ Connection Failed'}
              </h3>
              
              {result.success ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium">Server Information:</h4>
                    <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                      {JSON.stringify(result.server, null, 2)}
                    </pre>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Available Collections:</h4>
                    <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                      {JSON.stringify(result.collections, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div>
                  <h4 className="font-medium text-red-600">Error:</h4>
                  <pre className="bg-red-50 p-3 rounded text-sm text-red-700">
                    {result.error}
                  </pre>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
