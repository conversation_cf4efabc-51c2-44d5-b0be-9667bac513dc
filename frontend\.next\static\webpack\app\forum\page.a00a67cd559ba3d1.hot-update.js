"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        icon: _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/\",\n        active: true\n    }\n];\n// Icon mapping for different category types\nconst categoryIconMap = {\n    \"general\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    \"prayer\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    \"discussion\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    \"spiritual\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"community\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"tech\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"design\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"career\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"help\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"random\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"global\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n};\n// Color mapping for categories\nconst categoryColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-red-500\",\n    \"bg-pink-500\",\n    \"bg-amber-500\",\n    \"bg-indigo-500\",\n    \"bg-teal-500\",\n    \"bg-cyan-500\"\n];\n// Function to get icon for category\nconst getCategoryIcon = (categoryName)=>{\n    const key = categoryName.toLowerCase();\n    return categoryIconMap[key] || _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; // Default to BookOpen\n};\n// Function to get color for category\nconst getCategoryColor = (index)=>{\n    return categoryColors[index % categoryColors.length];\n};\nfunction Sidebar() {\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCategories = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_6__.api.getCategories(\"published\");\n                if (response && response.data) {\n                    setCategories(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching categories:\", err);\n                setError(\"Failed to load categories\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCategories();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n            className: \"h-full px-3 py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                children: \"Navigation\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: item.active ? \"secondary\" : \"ghost\",\n                                        className: \"w-full justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-red-500 px-3\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this) : categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground px-3\",\n                                    children: \"No categories found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, this) : categories.map((category, index)=>{\n                                    const IconComponent = getCategoryIcon(category.Category || \"\");\n                                    const color = getCategoryColor(index);\n                                    const postCount = Array.isArray(category.post) ? category.post.length : 0;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full mr-3 \".concat(color)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    category.Category\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: postCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Create Post\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"PEFWK5NudK8G7IEShrERrFoV+TY=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});