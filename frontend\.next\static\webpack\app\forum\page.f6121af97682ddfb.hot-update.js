"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.446.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronDown\", [\n  [\"path\", { d: \"m6 9 6 6 6-6\", key: \"qrunsl\" }]\n]);\n\n\n//# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsb0JBQW9CLGdFQUFnQjtBQUNwQyxhQUFhLGtDQUFrQztBQUMvQzs7QUFFa0M7QUFDbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLWRvd24uanM/NGUyYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC40NDYuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbihcIkNoZXZyb25Eb3duXCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTYgOSA2IDYgNi02XCIsIGtleTogXCJxcnVuc2xcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IENoZXZyb25Eb3duIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZXZyb24tZG93bi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.446.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronUp\", [[\"path\", { d: \"m18 15-6-6-6 6\", key: \"153udz\" }]]);\n\n\n//# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGtCQUFrQixnRUFBZ0IsMEJBQTBCLG9DQUFvQzs7QUFFaEU7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLXVwLmpzP2JiOWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDQ2LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBDaGV2cm9uVXAgPSBjcmVhdGVMdWNpZGVJY29uKFwiQ2hldnJvblVwXCIsIFtbXCJwYXRoXCIsIHsgZDogXCJtMTggMTUtNi02LTYgNlwiLCBrZXk6IFwiMTUzdWR6XCIgfV1dKTtcblxuZXhwb3J0IHsgQ2hldnJvblVwIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZXZyb24tdXAuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/create-post-modal.tsx":
/*!******************************************!*\
  !*** ./components/create-post-modal.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreatePostModal: function() { return /* binding */ CreatePostModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _barrel_optimize_names_X_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_X_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=X,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ CreatePostModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreatePostModal(param) {\n    let { open, onOpenChange, onPostCreated } = param;\n    _s();\n    const { user, isAuthenticated } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Form state\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isPublic, setIsPublic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch categories when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (open) {\n            fetchCategories();\n        }\n    }, [\n        open\n    ]);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_10__.api.getCategories(\"published\");\n            if (response && response.data) {\n                setCategories(response.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n        }\n    };\n    const handleAddTag = ()=>{\n        if (newTag.trim() && !tags.includes(newTag.trim())) {\n            setTags([\n                ...tags,\n                newTag.trim()\n            ]);\n            setNewTag(\"\");\n        }\n    };\n    const handleRemoveTag = (tagToRemove)=>{\n        setTags(tags.filter((tag)=>tag !== tagToRemove));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            e.preventDefault();\n            handleAddTag();\n        }\n    };\n    const resetForm = ()=>{\n        setTitle(\"\");\n        setDescription(\"\");\n        setSelectedCategory(\"\");\n        setTags([]);\n        setNewTag(\"\");\n        setIsPublic(true);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!isAuthenticated) {\n            alert(\"You must be logged in to create a post\");\n            return;\n        }\n        if (!title.trim() || !description.trim()) {\n            alert(\"Please fill in both title and description\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const authDirectus = (0,_lib_directus__WEBPACK_IMPORTED_MODULE_10__.getAuthenticatedDirectus)();\n            // Prepare post data\n            const postData = {\n                Title: title.trim(),\n                Description: description.trim(),\n                Tags: tags.length > 0 ? tags : null,\n                Is_Public: isPublic,\n                user: user === null || user === void 0 ? void 0 : user.id,\n                status: \"published\"\n            };\n            // Add category if selected\n            if (selectedCategory) {\n                // For M2M relationship, we need to handle it differently\n                // First create the post, then link the category\n                const postResponse = await authDirectus.post(\"/Posts\", postData);\n                if (postResponse.data && selectedCategory) {\n                    // Link the category to the post via the junction table\n                    await authDirectus.post(\"/Categories_Posts\", {\n                        Categories_id: parseInt(selectedCategory),\n                        Posts_id: postResponse.data.id\n                    });\n                }\n            } else {\n                await authDirectus.post(\"/Posts\", postData);\n            }\n            // Success\n            resetForm();\n            onOpenChange(false);\n            onPostCreated === null || onPostCreated === void 0 ? void 0 : onPostCreated();\n        } catch (error) {\n            console.error(\"Error creating post:\", error);\n            alert(\"Failed to create post. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[600px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            children: \"Create New Post\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Share your thoughts with the community\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"title\",\n                                    children: \"Title *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"title\",\n                                    placeholder: \"Enter post title...\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"description\",\n                                    children: \"Description *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                    id: \"description\",\n                                    placeholder: \"Write your post content...\",\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    className: \"min-h-[120px]\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"category\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                    value: selectedCategory,\n                                    onValueChange: setSelectedCategory,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                placeholder: \"Select a category (optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: category.id.toString(),\n                                                    children: category.Category\n                                                }, category.id, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"tags\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"tags\",\n                                            placeholder: \"Add a tag...\",\n                                            value: newTag,\n                                            onChange: (e)=>setNewTag(e.target.value),\n                                            onKeyPress: handleKeyPress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: handleAddTag,\n                                            size: \"sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mt-2\",\n                                    children: tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                \"#\",\n                                                tag,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>handleRemoveTag(tag),\n                                                    className: \"ml-1 hover:text-red-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                    id: \"public\",\n                                    checked: isPublic,\n                                    onCheckedChange: setIsPublic\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"public\",\n                                    children: \"Make this post public\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || !title.trim() || !description.trim(),\n                                    children: loading ? \"Creating...\" : \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\create-post-modal.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(CreatePostModal, \"SLfiCG/TpJV+m4GS96lrct6UcII=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_11__.useAuth\n    ];\n});\n_c = CreatePostModal;\nvar _c;\n$RefreshReg$(_c, \"CreatePostModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/create-post-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _create_post_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./create-post-modal */ \"(app-pages-browser)/./components/create-post-modal.tsx\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        icon: _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: \"/\",\n        active: true\n    }\n];\n// Icon mapping for different category types\nconst categoryIconMap = {\n    \"general\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    \"prayer\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"discussion\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"spiritual\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"community\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"tech\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"design\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"career\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"help\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"random\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"global\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n};\n// Color mapping for categories\nconst categoryColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-red-500\",\n    \"bg-pink-500\",\n    \"bg-amber-500\",\n    \"bg-indigo-500\",\n    \"bg-teal-500\",\n    \"bg-cyan-500\"\n];\n// Function to get icon for category\nconst getCategoryIcon = (categoryName)=>{\n    const key = categoryName.toLowerCase();\n    return categoryIconMap[key] || _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]; // Default to BookOpen\n};\n// Function to get color for category\nconst getCategoryColor = (index)=>{\n    return categoryColors[index % categoryColors.length];\n};\nfunction Sidebar() {\n    _s();\n    const { isAuthenticated } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCategories = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_6__.api.getCategories(\"published\");\n                if (response && response.data) {\n                    setCategories(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching categories:\", err);\n                setError(\"Failed to load categories\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCategories();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                className: \"h-full px-3 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: item.active ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-red-500 px-3\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this) : categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground px-3\",\n                                        children: \"No categories found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this) : categories.map((category, index)=>{\n                                        const IconComponent = getCategoryIcon(category.Category || \"\");\n                                        const color = getCategoryColor(index);\n                                        const postCount = Array.isArray(category.post) ? category.post.length : 0;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        category.Category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: postCount\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>setShowCreatePost(true),\n                                            disabled: !isAuthenticated,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Post\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground px-3 mt-1\",\n                                            children: \"Sign in to create posts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_create_post_modal__WEBPACK_IMPORTED_MODULE_7__.CreatePostModal, {\n                open: showCreatePost,\n                onOpenChange: setShowCreatePost,\n                onPostCreated: ()=>{\n                    // Optionally refresh the page or emit an event\n                    window.location.reload();\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"eKQ6XY8IzM5dXH0e273g9TqM09Y=\", false, function() {\n    return [\n        _auth_provider__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/select.tsx":
/*!**********************************!*\
  !*** ./components/ui/select.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: function() { return /* binding */ Select; },\n/* harmony export */   SelectContent: function() { return /* binding */ SelectContent; },\n/* harmony export */   SelectGroup: function() { return /* binding */ SelectGroup; },\n/* harmony export */   SelectItem: function() { return /* binding */ SelectItem; },\n/* harmony export */   SelectLabel: function() { return /* binding */ SelectLabel; },\n/* harmony export */   SelectScrollDownButton: function() { return /* binding */ SelectScrollDownButton; },\n/* harmony export */   SelectScrollUpButton: function() { return /* binding */ SelectScrollUpButton; },\n/* harmony export */   SelectSeparator: function() { return /* binding */ SelectSeparator; },\n/* harmony export */   SelectTrigger: function() { return /* binding */ SelectTrigger; },\n/* harmony export */   SelectValue: function() { return /* binding */ SelectValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectGroup,SelectValue,SelectTrigger,SelectContent,SelectLabel,SelectItem,SelectSeparator,SelectScrollUpButton,SelectScrollDownButton auto */ \n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>/*#__PURE__*/ {\n    let { className, children, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 28,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = SelectTrigger;\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>/*#__PURE__*/ {\n    let { className, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = SelectScrollUpButton;\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>/*#__PURE__*/ {\n    let { className, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = SelectScrollDownButton;\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>/*#__PURE__*/ {\n    let { className, children, position = \"popper\", ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = SelectContent;\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>/*#__PURE__*/ {\n    let { className, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 106,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = SelectLabel;\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>/*#__PURE__*/ {\n    let { className, children, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 132,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 118,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = SelectItem;\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>/*#__PURE__*/ {\n    let { className, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 141,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = SelectSeparator;\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"SelectTrigger$React.forwardRef\");\n$RefreshReg$(_c1, \"SelectTrigger\");\n$RefreshReg$(_c2, \"SelectScrollUpButton\");\n$RefreshReg$(_c3, \"SelectScrollDownButton\");\n$RefreshReg$(_c4, \"SelectContent$React.forwardRef\");\n$RefreshReg$(_c5, \"SelectContent\");\n$RefreshReg$(_c6, \"SelectLabel$React.forwardRef\");\n$RefreshReg$(_c7, \"SelectLabel\");\n$RefreshReg$(_c8, \"SelectItem$React.forwardRef\");\n$RefreshReg$(_c9, \"SelectItem\");\n$RefreshReg$(_c10, \"SelectSeparator$React.forwardRef\");\n$RefreshReg$(_c11, \"SelectSeparator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/select.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/switch.tsx":
/*!**********************************!*\
  !*** ./components/ui/switch.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: function() { return /* binding */ Switch; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-switch */ \"(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\n\nconst Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>/*#__PURE__*/ {\n    let { className, ...props } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\", className),\n        ...props,\n        ref: ref,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\")\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\switch.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\switch.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Switch;\nSwitch.displayName = _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Switch$React.forwardRef\");\n$RefreshReg$(_c1, \"Switch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/switch.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: function() { return /* binding */ Textarea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Textarea;\nTextarea.displayName = \"Textarea\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Textarea$React.forwardRef\");\n$RefreshReg$(_c1, \"Textarea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErQjtBQUVFO0FBS2pDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsTUFDL0IsUUFBMEJJO1FBQXpCLEVBQUVDLFNBQVMsRUFBRSxHQUFHQyxPQUFPO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXSiw4Q0FBRUEsQ0FDWCx3U0FDQUk7UUFFRkQsS0FBS0E7UUFDSixHQUFHRSxLQUFLOzs7Ozs7QUFHZjs7QUFFRkosU0FBU00sV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3g/YjgwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRhcmVhUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+IHt9XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgJ2ZsZXggbWluLWgtWzgwcHhdIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAnLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApO1xuICB9XG4pO1xuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSAnVGV4dGFyZWEnO1xuXG5leHBvcnQgeyBUZXh0YXJlYSB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJyZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/textarea.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: function() { return /* binding */ Arrow2; },\n/* harmony export */   Content: function() { return /* binding */ Content2; },\n/* harmony export */   Group: function() { return /* binding */ Group; },\n/* harmony export */   Icon: function() { return /* binding */ Icon; },\n/* harmony export */   Item: function() { return /* binding */ Item; },\n/* harmony export */   ItemIndicator: function() { return /* binding */ ItemIndicator; },\n/* harmony export */   ItemText: function() { return /* binding */ ItemText; },\n/* harmony export */   Label: function() { return /* binding */ Label; },\n/* harmony export */   Portal: function() { return /* binding */ Portal; },\n/* harmony export */   Root: function() { return /* binding */ Root2; },\n/* harmony export */   ScrollDownButton: function() { return /* binding */ ScrollDownButton; },\n/* harmony export */   ScrollUpButton: function() { return /* binding */ ScrollUpButton; },\n/* harmony export */   Select: function() { return /* binding */ Select; },\n/* harmony export */   SelectArrow: function() { return /* binding */ SelectArrow; },\n/* harmony export */   SelectContent: function() { return /* binding */ SelectContent; },\n/* harmony export */   SelectGroup: function() { return /* binding */ SelectGroup; },\n/* harmony export */   SelectIcon: function() { return /* binding */ SelectIcon; },\n/* harmony export */   SelectItem: function() { return /* binding */ SelectItem; },\n/* harmony export */   SelectItemIndicator: function() { return /* binding */ SelectItemIndicator; },\n/* harmony export */   SelectItemText: function() { return /* binding */ SelectItemText; },\n/* harmony export */   SelectLabel: function() { return /* binding */ SelectLabel; },\n/* harmony export */   SelectPortal: function() { return /* binding */ SelectPortal; },\n/* harmony export */   SelectScrollDownButton: function() { return /* binding */ SelectScrollDownButton; },\n/* harmony export */   SelectScrollUpButton: function() { return /* binding */ SelectScrollUpButton; },\n/* harmony export */   SelectSeparator: function() { return /* binding */ SelectSeparator; },\n/* harmony export */   SelectTrigger: function() { return /* binding */ SelectTrigger; },\n/* harmony export */   SelectValue: function() { return /* binding */ SelectValue; },\n/* harmony export */   SelectViewport: function() { return /* binding */ SelectViewport; },\n/* harmony export */   Separator: function() { return /* binding */ Separator; },\n/* harmony export */   Trigger: function() { return /* binding */ Trigger; },\n/* harmony export */   Value: function() { return /* binding */ Value; },\n/* harmony export */   Viewport: function() { return /* binding */ Viewport; },\n/* harmony export */   createSelectScope: function() { return /* binding */ createSelectScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\"use client\";\n\n// packages/react/select/src/Select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\" \", \"Enter\", \"ArrowUp\", \"ArrowDown\"];\nvar SELECTION_KEYS = [\" \", \"Enter\"];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n  createCollectionScope,\n  _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n  const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange\n  });\n  const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange\n  });\n  const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n  const nativeSelectKey = Array.from(nativeOptionsSet).map((option) => option.props.value).join(\";\");\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, { ...popperScope, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(\n    SelectProvider,\n    {\n      required,\n      scope: __scopeSelect,\n      trigger,\n      onTriggerChange: setTrigger,\n      valueNode,\n      onValueNodeChange: setValueNode,\n      valueNodeHasChildren,\n      onValueNodeHasChildrenChange: setValueNodeHasChildren,\n      contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n      value,\n      onValueChange: setValue,\n      open,\n      onOpenChange: setOpen,\n      dir: direction,\n      triggerPointerDownPosRef,\n      disabled,\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, { scope: __scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          SelectNativeOptionsProvider,\n          {\n            scope: props.__scopeSelect,\n            onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, []),\n            onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, []),\n            children\n          }\n        ) }),\n        isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(\n          BubbleSelect,\n          {\n            \"aria-hidden\": true,\n            required,\n            tabIndex: -1,\n            name,\n            autoComplete,\n            value,\n            onChange: (event) => setValue(event.target.value),\n            disabled,\n            form,\n            children: [\n              value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", { value: \"\" }) : null,\n              Array.from(nativeOptionsSet)\n            ]\n          },\n          nativeSelectKey\n        ) : null\n      ]\n    }\n  ) });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== void 0) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n    const handleOpen = (pointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        resetTypeahead();\n      }\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY)\n        };\n      }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button,\n      {\n        type: \"button\",\n        role: \"combobox\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open,\n        \"aria-required\": context.required,\n        \"aria-autocomplete\": \"none\",\n        dir: context.dir,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        disabled: isDisabled,\n        \"data-disabled\": isDisabled ? \"\" : void 0,\n        \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n        ...triggerProps,\n        ref: composedRefs,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event) => {\n          event.currentTarget.focus();\n          if (pointerTypeRef.current !== \"mouse\") {\n            handleOpen(event);\n          }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event) => {\n          pointerTypeRef.current = event.pointerType;\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n          }\n          if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n            handleOpen(event);\n            event.preventDefault();\n          }\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event) => {\n          const isTypingAhead = searchRef.current !== \"\";\n          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n          if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n          if (isTypingAhead && event.key === \" \") return;\n          if (OPEN_KEYS.includes(event.key)) {\n            handleOpen();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span,\n      {\n        ...valueProps,\n        ref: composedRefs,\n        style: { pointerEvents: \"none\" },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: placeholder }) : children\n      }\n    );\n  }\n);\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { \"aria-hidden\": true, ...iconProps, ref: forwardedRef, children: children || \"\\u25BC\" });\n  }\n);\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, { asChild: true, ...props });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n      const frag = fragment;\n      return frag ? react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, { scope: props.__scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, { scope: props.__scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", { children: props.children }) }) }),\n        frag\n      ) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, { ...props, ref: forwardedRef });\n  }\n);\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = \"item-aligned\",\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n    }, [content]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (candidates) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: \"nearest\" });\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n        const handlePointerMove = (event) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n          };\n        };\n        const handlePointerUp = (event) => {\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            if (!content.contains(event.target)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener(\"pointermove\", handlePointerMove);\n          document.addEventListener(\"pointerup\", handlePointerUp, { capture: true, once: true });\n        }\n        return () => {\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          document.removeEventListener(\"pointerup\", handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener(\"blur\", close);\n      window.addEventListener(\"resize\", close);\n      return () => {\n        window.removeEventListener(\"blur\", close);\n        window.removeEventListener(\"resize\", close);\n      };\n    }, [onOpenChange]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        setTimeout(() => nextItem.ref.current.focus());\n      }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      SelectContentProvider,\n      {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], { as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot, allowPinchZoom: true, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope,\n          {\n            asChild: true,\n            trapped: context.open,\n            onMountAutoFocus: (event) => {\n              event.preventDefault();\n            },\n            onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            }),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n              _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents: true,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside: (event) => event.preventDefault(),\n                onDismiss: () => context.onOpenChange(false),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n                  SelectPosition,\n                  {\n                    role: \"listbox\",\n                    id: context.contentId,\n                    \"data-state\": context.open ? \"open\" : \"closed\",\n                    dir: context.dir,\n                    onContextMenu: (event) => event.preventDefault(),\n                    ...contentProps,\n                    ...popperContentProps,\n                    onPlaced: () => setIsPositioned(true),\n                    ref: composedRefs,\n                    style: {\n                      // flex layout so we can place the scroll buttons properly\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      // reset the outline by default as the content MAY get focused\n                      outline: \"none\",\n                      ...contentProps.style\n                    },\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event) => {\n                      const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                      if (event.key === \"Tab\") event.preventDefault();\n                      if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                      if ([\"ArrowUp\", \"ArrowDown\", \"Home\", \"End\"].includes(event.key)) {\n                        const items = getItems().filter((item) => !item.disabled);\n                        let candidateNodes = items.map((item) => item.ref.current);\n                        if ([\"ArrowUp\", \"End\"].includes(event.key)) {\n                          candidateNodes = candidateNodes.slice().reverse();\n                        }\n                        if ([\"ArrowUp\", \"ArrowDown\"].includes(event.key)) {\n                          const currentElement = event.target;\n                          const currentIndex = candidateNodes.indexOf(currentElement);\n                          candidateNodes = candidateNodes.slice(currentIndex + 1);\n                        }\n                        setTimeout(() => focusFirst(candidateNodes));\n                        event.preventDefault();\n                      }\n                    })\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n      if (context.dir !== \"rtl\") {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.left = clampedLeft + \"px\";\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.right = clampedRight + \"px\";\n      }\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = \"0px\";\n        const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n          (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + \"px\";\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = \"0px\";\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n          (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + \"px\";\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + \"px\";\n      contentWrapper.style.maxHeight = availableHeight + \"px\";\n      onPlaced?.();\n      requestAnimationFrame(() => shouldExpandOnScrollRef.current = true);\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced\n  ]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => position(), [position]);\n  const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectViewportProvider,\n    {\n      scope: __scopeSelect,\n      contentWrapper,\n      shouldExpandOnScrollRef,\n      onScrollButtonChange: handleScrollButtonChange,\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"div\",\n        {\n          ref: setContentWrapper,\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"fixed\",\n            zIndex: contentZIndex\n          },\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n            _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n            {\n              ...popperProps,\n              ref: composedRefs,\n              style: {\n                // When we get the height of the content, it includes borders. If we were to set\n                // the height without having `boxSizing: 'border-box'` it would be too big.\n                boxSizing: \"border-box\",\n                // We need to ensure the content doesn't get taller than the wrapper\n                maxHeight: \"100%\",\n                ...popperProps.style\n              }\n            }\n          )\n        }\n      )\n    }\n  );\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = \"start\",\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content,\n    {\n      ...popperScope,\n      ...popperProps,\n      ref: forwardedRef,\n      align,\n      collisionPadding,\n      style: {\n        // Ensure border-box for floating-ui calculations\n        boxSizing: \"border-box\",\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, { scope: __scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n        {\n          \"data-radix-select-viewport\": \"\",\n          role: \"presentation\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            // we use position: 'relative' here on the `viewport` so that when we call\n            // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n            // (independent of the scrollUpButton).\n            position: \"relative\",\n            flex: 1,\n            // Viewport should only be scrollable in the vertical direction.\n            // This won't work in vertical writing modes, so we'll need to\n            // revisit this if/when that is supported\n            // https://developer.chrome.com/blog/vertical-form-controls\n            overflow: \"hidden auto\",\n            ...viewportProps.style\n          },\n          onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event) => {\n            const viewport = event.currentTarget;\n            const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n            if (shouldExpandOnScrollRef?.current && contentWrapper) {\n              const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n              if (scrolledBy > 0) {\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                const cssHeight = parseFloat(contentWrapper.style.height);\n                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                if (prevHeight < availableHeight) {\n                  const nextHeight = prevHeight + scrolledBy;\n                  const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                  const heightDiff = nextHeight - clampedNextHeight;\n                  contentWrapper.style.height = clampedNextHeight + \"px\";\n                  if (contentWrapper.style.bottom === \"0px\") {\n                    viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                    contentWrapper.style.justifyContent = \"flex-end\";\n                  }\n                }\n              }\n            }\n            prevScrollTopRef.current = viewport.scrollTop;\n          })\n        }\n      ) })\n    ] });\n  }\n);\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, { scope: __scopeSelect, id: groupId, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { role: \"group\", \"aria-labelledby\": groupId, ...groupProps, ref: forwardedRef }) });\n  }\n);\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { id: groupContext.id, ...labelProps, ref: forwardedRef });\n  }\n);\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(\n      forwardedRef,\n      (node) => contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n    if (value === \"\") {\n      throw new Error(\n        \"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\"\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      SelectItemContextProvider,\n      {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          Collection.ItemSlot,\n          {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n              _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n              {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, () => setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, () => setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, () => {\n                  if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, () => {\n                  if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                  if (disabled) {\n                    contentContext.onItemLeave?.();\n                  } else if (pointerTypeRef.current === \"mouse\") {\n                    event.currentTarget.focus({ preventScroll: true });\n                  }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event) => {\n                  if (event.currentTarget === document.activeElement) {\n                    contentContext.onItemLeave?.();\n                  }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event) => {\n                  const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                  if (isTypingAhead && event.key === \" \") return;\n                  if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                  if (event.key === \" \") event.preventDefault();\n                })\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n      () => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", { value: itemContext.value, disabled: itemContext.disabled, children: textContent }, itemContext.value),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { id: itemContext.textId, ...itemTextProps, ref: composedRefs }),\n      itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n    ] });\n  }\n);\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { \"aria-hidden\": true, ...itemIndicatorProps, ref: forwardedRef }) : null;\n  }\n);\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const canScrollUp2 = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n  const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const getItems = useCollection(__scopeSelect);\n  const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: \"nearest\" });\n  }, [getItems]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n    {\n      \"aria-hidden\": true,\n      ...scrollIndicatorProps,\n      ref: forwardedRef,\n      style: { flexShrink: 0, ...scrollIndicatorProps.style },\n      onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })\n    }\n  );\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { \"aria-hidden\": true, ...separatorProps, ref: forwardedRef });\n  }\n);\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef }) : null;\n  }\n);\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n  return value === \"\" || value === void 0;\n}\nvar BubbleSelect = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const select = ref.current;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        \"value\"\n      );\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"change\", { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, { asChild: true, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", { ...selectProps, ref: composedRefs, defaultValue: value }) });\n  }\n);\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n  const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n  const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n  const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (key) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n  const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    searchRef.current = \"\";\n    window.clearTimeout(timerRef.current);\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n  return [searchRef, handleTypeaheadSearch, resetTypeahead];\n}\nfunction findNextItem(items, search, currentItem) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find(\n    (item) => item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   Switch: function() { return /* binding */ Switch; },\n/* harmony export */   SwitchThumb: function() { return /* binding */ SwitchThumb; },\n/* harmony export */   Thumb: function() { return /* binding */ Thumb; },\n/* harmony export */   createSwitchScope: function() { return /* binding */ createSwitchScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\"use client\";\n\n// packages/react/switch/src/Switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = \"on\",\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, { scope: __scopeSwitch, checked, disabled, children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button,\n        {\n          type: \"button\",\n          role: \"switch\",\n          \"aria-checked\": checked,\n          \"aria-required\": required,\n          \"data-state\": getState(checked),\n          \"data-disabled\": disabled ? \"\" : void 0,\n          disabled,\n          value,\n          ...switchProps,\n          ref: composedRefs,\n          onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })\n        }\n      ),\n      isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        BubbleInput,\n        {\n          control: button,\n          bubbles: !hasConsumerStoppedPropagationRef.current,\n          name,\n          value,\n          checked,\n          required,\n          disabled,\n          form,\n          style: { transform: \"translateX(-100%)\" }\n        }\n      )\n    ] });\n  }\n);\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span,\n      {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nSwitchThumb.displayName = THUMB_NAME;\nvar BubbleInput = (props) => {\n  const { control, checked, bubbles = true, ...inputProps } = props;\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n  const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const input = ref.current;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event(\"click\", { bubbles });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"input\",\n    {\n      type: \"checkbox\",\n      \"aria-hidden\": true,\n      defaultChecked: checked,\n      ...inputProps,\n      tabIndex: -1,\n      ref,\n      style: {\n        ...props.style,\n        ...controlSize,\n        position: \"absolute\",\n        pointerEvents: \"none\",\n        opacity: 0,\n        margin: 0\n      }\n    }\n  );\n};\nfunction getState(checked) {\n  return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtc3dpdGNoL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0FBRUE7QUFDK0I7QUFDNEI7QUFDSTtBQUNGO0FBQ2lCO0FBQ25CO0FBQ1I7QUFDRztBQUNSO0FBQzlDO0FBQ0EsK0NBQStDLDJFQUFrQjtBQUNqRTtBQUNBLGFBQWEsNkNBQWdCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixnQ0FBZ0MsMkNBQWM7QUFDOUMseUJBQXlCLDZFQUFlO0FBQ3hDLDZDQUE2Qyx5Q0FBWTtBQUN6RDtBQUNBLDBDQUEwQyw0RkFBb0I7QUFDOUQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLDJCQUEyQix1REFBSSxtQkFBbUI7QUFDbEQsc0JBQXNCLHNEQUFHO0FBQ3pCLFFBQVEsZ0VBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5RUFBb0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsdUNBQXVDLHNEQUFHO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNkNBQWdCO0FBQ2xDO0FBQ0EsWUFBWSwrQkFBK0I7QUFDM0M7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGtEQUFrRDtBQUM1RCxjQUFjLHlDQUFZO0FBQzFCLHNCQUFzQix5RUFBVztBQUNqQyxzQkFBc0IsaUVBQU87QUFDN0IsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLFNBQVM7QUFDbEQ7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHlCQUF5QixzREFBRztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFPRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtc3dpdGNoL2Rpc3QvaW5kZXgubWpzPzk2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHBhY2thZ2VzL3JlYWN0L3N3aXRjaC9zcmMvU3dpdGNoLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gXCJAcmFkaXgtdWkvcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmc1wiO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1jb250ZXh0XCI7XG5pbXBvcnQgeyB1c2VDb250cm9sbGFibGVTdGF0ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZVwiO1xuaW1wb3J0IHsgdXNlUHJldmlvdXMgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91c1wiO1xuaW1wb3J0IHsgdXNlU2l6ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLXNpemVcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyBqc3gsIGpzeHMgfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBTV0lUQ0hfTkFNRSA9IFwiU3dpdGNoXCI7XG52YXIgW2NyZWF0ZVN3aXRjaENvbnRleHQsIGNyZWF0ZVN3aXRjaFNjb3BlXSA9IGNyZWF0ZUNvbnRleHRTY29wZShTV0lUQ0hfTkFNRSk7XG52YXIgW1N3aXRjaFByb3ZpZGVyLCB1c2VTd2l0Y2hDb250ZXh0XSA9IGNyZWF0ZVN3aXRjaENvbnRleHQoU1dJVENIX05BTUUpO1xudmFyIFN3aXRjaCA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZVN3aXRjaCxcbiAgICAgIG5hbWUsXG4gICAgICBjaGVja2VkOiBjaGVja2VkUHJvcCxcbiAgICAgIGRlZmF1bHRDaGVja2VkLFxuICAgICAgcmVxdWlyZWQsXG4gICAgICBkaXNhYmxlZCxcbiAgICAgIHZhbHVlID0gXCJvblwiLFxuICAgICAgb25DaGVja2VkQ2hhbmdlLFxuICAgICAgZm9ybSxcbiAgICAgIC4uLnN3aXRjaFByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IFtidXR0b24sIHNldEJ1dHRvbl0gPSBSZWFjdC51c2VTdGF0ZShudWxsKTtcbiAgICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMoZm9yd2FyZGVkUmVmLCAobm9kZSkgPT4gc2V0QnV0dG9uKG5vZGUpKTtcbiAgICBjb25zdCBoYXNDb25zdW1lclN0b3BwZWRQcm9wYWdhdGlvblJlZiA9IFJlYWN0LnVzZVJlZihmYWxzZSk7XG4gICAgY29uc3QgaXNGb3JtQ29udHJvbCA9IGJ1dHRvbiA/IGZvcm0gfHwgISFidXR0b24uY2xvc2VzdChcImZvcm1cIikgOiB0cnVlO1xuICAgIGNvbnN0IFtjaGVja2VkID0gZmFsc2UsIHNldENoZWNrZWRdID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgICAgcHJvcDogY2hlY2tlZFByb3AsXG4gICAgICBkZWZhdWx0UHJvcDogZGVmYXVsdENoZWNrZWQsXG4gICAgICBvbkNoYW5nZTogb25DaGVja2VkQ2hhbmdlXG4gICAgfSk7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3hzKFN3aXRjaFByb3ZpZGVyLCB7IHNjb3BlOiBfX3Njb3BlU3dpdGNoLCBjaGVja2VkLCBkaXNhYmxlZCwgY2hpbGRyZW46IFtcbiAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICAgIFByaW1pdGl2ZS5idXR0b24sXG4gICAgICAgIHtcbiAgICAgICAgICB0eXBlOiBcImJ1dHRvblwiLFxuICAgICAgICAgIHJvbGU6IFwic3dpdGNoXCIsXG4gICAgICAgICAgXCJhcmlhLWNoZWNrZWRcIjogY2hlY2tlZCxcbiAgICAgICAgICBcImFyaWEtcmVxdWlyZWRcIjogcmVxdWlyZWQsXG4gICAgICAgICAgXCJkYXRhLXN0YXRlXCI6IGdldFN0YXRlKGNoZWNrZWQpLFxuICAgICAgICAgIFwiZGF0YS1kaXNhYmxlZFwiOiBkaXNhYmxlZCA/IFwiXCIgOiB2b2lkIDAsXG4gICAgICAgICAgZGlzYWJsZWQsXG4gICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgLi4uc3dpdGNoUHJvcHMsXG4gICAgICAgICAgcmVmOiBjb21wb3NlZFJlZnMsXG4gICAgICAgICAgb25DbGljazogY29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25DbGljaywgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICBzZXRDaGVja2VkKChwcmV2Q2hlY2tlZCkgPT4gIXByZXZDaGVja2VkKTtcbiAgICAgICAgICAgIGlmIChpc0Zvcm1Db250cm9sKSB7XG4gICAgICAgICAgICAgIGhhc0NvbnN1bWVyU3RvcHBlZFByb3BhZ2F0aW9uUmVmLmN1cnJlbnQgPSBldmVudC5pc1Byb3BhZ2F0aW9uU3RvcHBlZCgpO1xuICAgICAgICAgICAgICBpZiAoIWhhc0NvbnN1bWVyU3RvcHBlZFByb3BhZ2F0aW9uUmVmLmN1cnJlbnQpIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICksXG4gICAgICBpc0Zvcm1Db250cm9sICYmIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICAgIEJ1YmJsZUlucHV0LFxuICAgICAgICB7XG4gICAgICAgICAgY29udHJvbDogYnV0dG9uLFxuICAgICAgICAgIGJ1YmJsZXM6ICFoYXNDb25zdW1lclN0b3BwZWRQcm9wYWdhdGlvblJlZi5jdXJyZW50LFxuICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgY2hlY2tlZCxcbiAgICAgICAgICByZXF1aXJlZCxcbiAgICAgICAgICBkaXNhYmxlZCxcbiAgICAgICAgICBmb3JtLFxuICAgICAgICAgIHN0eWxlOiB7IHRyYW5zZm9ybTogXCJ0cmFuc2xhdGVYKC0xMDAlKVwiIH1cbiAgICAgICAgfVxuICAgICAgKVxuICAgIF0gfSk7XG4gIH1cbik7XG5Td2l0Y2guZGlzcGxheU5hbWUgPSBTV0lUQ0hfTkFNRTtcbnZhciBUSFVNQl9OQU1FID0gXCJTd2l0Y2hUaHVtYlwiO1xudmFyIFN3aXRjaFRodW1iID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVTd2l0Y2gsIC4uLnRodW1iUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VTd2l0Y2hDb250ZXh0KFRIVU1CX05BTUUsIF9fc2NvcGVTd2l0Y2gpO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgUHJpbWl0aXZlLnNwYW4sXG4gICAgICB7XG4gICAgICAgIFwiZGF0YS1zdGF0ZVwiOiBnZXRTdGF0ZShjb250ZXh0LmNoZWNrZWQpLFxuICAgICAgICBcImRhdGEtZGlzYWJsZWRcIjogY29udGV4dC5kaXNhYmxlZCA/IFwiXCIgOiB2b2lkIDAsXG4gICAgICAgIC4uLnRodW1iUHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmXG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcblN3aXRjaFRodW1iLmRpc3BsYXlOYW1lID0gVEhVTUJfTkFNRTtcbnZhciBCdWJibGVJbnB1dCA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IGNvbnRyb2wsIGNoZWNrZWQsIGJ1YmJsZXMgPSB0cnVlLCAuLi5pbnB1dFByb3BzIH0gPSBwcm9wcztcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICBjb25zdCBwcmV2Q2hlY2tlZCA9IHVzZVByZXZpb3VzKGNoZWNrZWQpO1xuICBjb25zdCBjb250cm9sU2l6ZSA9IHVzZVNpemUoY29udHJvbCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSByZWYuY3VycmVudDtcbiAgICBjb25zdCBpbnB1dFByb3RvID0gd2luZG93LkhUTUxJbnB1dEVsZW1lbnQucHJvdG90eXBlO1xuICAgIGNvbnN0IGRlc2NyaXB0b3IgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGlucHV0UHJvdG8sIFwiY2hlY2tlZFwiKTtcbiAgICBjb25zdCBzZXRDaGVja2VkID0gZGVzY3JpcHRvci5zZXQ7XG4gICAgaWYgKHByZXZDaGVja2VkICE9PSBjaGVja2VkICYmIHNldENoZWNrZWQpIHtcbiAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEV2ZW50KFwiY2xpY2tcIiwgeyBidWJibGVzIH0pO1xuICAgICAgc2V0Q2hlY2tlZC5jYWxsKGlucHV0LCBjaGVja2VkKTtcbiAgICAgIGlucHV0LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xuICAgIH1cbiAgfSwgW3ByZXZDaGVja2VkLCBjaGVja2VkLCBidWJibGVzXSk7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgIFwiaW5wdXRcIixcbiAgICB7XG4gICAgICB0eXBlOiBcImNoZWNrYm94XCIsXG4gICAgICBcImFyaWEtaGlkZGVuXCI6IHRydWUsXG4gICAgICBkZWZhdWx0Q2hlY2tlZDogY2hlY2tlZCxcbiAgICAgIC4uLmlucHV0UHJvcHMsXG4gICAgICB0YWJJbmRleDogLTEsXG4gICAgICByZWYsXG4gICAgICBzdHlsZToge1xuICAgICAgICAuLi5wcm9wcy5zdHlsZSxcbiAgICAgICAgLi4uY29udHJvbFNpemUsXG4gICAgICAgIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gICAgICAgIHBvaW50ZXJFdmVudHM6IFwibm9uZVwiLFxuICAgICAgICBvcGFjaXR5OiAwLFxuICAgICAgICBtYXJnaW46IDBcbiAgICAgIH1cbiAgICB9XG4gICk7XG59O1xuZnVuY3Rpb24gZ2V0U3RhdGUoY2hlY2tlZCkge1xuICByZXR1cm4gY2hlY2tlZCA/IFwiY2hlY2tlZFwiIDogXCJ1bmNoZWNrZWRcIjtcbn1cbnZhciBSb290ID0gU3dpdGNoO1xudmFyIFRodW1iID0gU3dpdGNoVGh1bWI7XG5leHBvcnQge1xuICBSb290LFxuICBTd2l0Y2gsXG4gIFN3aXRjaFRodW1iLFxuICBUaHVtYixcbiAgY3JlYXRlU3dpdGNoU2NvcGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: function() { return /* binding */ usePrevious; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// packages/react/use-previous/src/usePrevious.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLXByZXZpb3VzL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0I7QUFDQSxjQUFjLHlDQUFZLEdBQUcsd0JBQXdCO0FBQ3JELFNBQVMsMENBQWE7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMvZGlzdC9pbmRleC5tanM/Mjk0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtcHJldmlvdXMvc3JjL3VzZVByZXZpb3VzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VQcmV2aW91cyh2YWx1ZSkge1xuICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWYoeyB2YWx1ZSwgcHJldmlvdXM6IHZhbHVlIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKHJlZi5jdXJyZW50LnZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgcmVmLmN1cnJlbnQucHJldmlvdXMgPSByZWYuY3VycmVudC52YWx1ZTtcbiAgICAgIHJlZi5jdXJyZW50LnZhbHVlID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC5wcmV2aW91cztcbiAgfSwgW3ZhbHVlXSk7XG59XG5leHBvcnQge1xuICB1c2VQcmV2aW91c1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   VisuallyHidden: function() { return /* binding */ VisuallyHidden; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// packages/react/visually-hidden/src/VisuallyHidden.tsx\n\n\n\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdmlzdWFsbHktaGlkZGVuL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBLHFCQUFxQiw2Q0FBZ0I7QUFDckM7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdmlzdWFsbHktaGlkZGVuL2Rpc3QvaW5kZXgubWpzPzc5NDIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdmlzdWFsbHktaGlkZGVuL3NyYy9WaXN1YWxseUhpZGRlbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIE5BTUUgPSBcIlZpc3VhbGx5SGlkZGVuXCI7XG52YXIgVmlzdWFsbHlIaWRkZW4gPSBSZWFjdC5mb3J3YXJkUmVmKFxuICAocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgUHJpbWl0aXZlLnNwYW4sXG4gICAgICB7XG4gICAgICAgIC4uLnByb3BzLFxuICAgICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS90d2JzL2Jvb3RzdHJhcC9ibG9iL21hc3Rlci9zY3NzL21peGlucy9fc2NyZWVuLXJlYWRlci5zY3NzXG4gICAgICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgICAgICAgICBib3JkZXI6IDAsXG4gICAgICAgICAgd2lkdGg6IDEsXG4gICAgICAgICAgaGVpZ2h0OiAxLFxuICAgICAgICAgIHBhZGRpbmc6IDAsXG4gICAgICAgICAgbWFyZ2luOiAtMSxcbiAgICAgICAgICBvdmVyZmxvdzogXCJoaWRkZW5cIixcbiAgICAgICAgICBjbGlwOiBcInJlY3QoMCwgMCwgMCwgMClcIixcbiAgICAgICAgICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxuICAgICAgICAgIHdvcmRXcmFwOiBcIm5vcm1hbFwiLFxuICAgICAgICAgIC4uLnByb3BzLnN0eWxlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICApO1xuICB9XG4pO1xuVmlzdWFsbHlIaWRkZW4uZGlzcGxheU5hbWUgPSBOQU1FO1xudmFyIFJvb3QgPSBWaXN1YWxseUhpZGRlbjtcbmV4cG9ydCB7XG4gIFJvb3QsXG4gIFZpc3VhbGx5SGlkZGVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n"));

/***/ })

});