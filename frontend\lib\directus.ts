import axios from 'axios';

// Directus configuration
const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
const DIRECTUS_TOKEN = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN;

// Create axios instance for Directus API
export const directus = axios.create({
  baseURL: `${DIRECTUS_URL}/items`,
  headers: {
    'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

// Authentication endpoints
export const auth = axios.create({
  baseURL: `${DIRECTUS_URL}/auth`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for Directus collections
export interface DirectusPost {
  id: string;
  title: string;
  content: string;
  author: string;
  category: string;
  tags: string[];
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
  likes_count: number;
  comments_count: number;
}

export interface DirectusComment {
  id: string;
  post: string;
  author: string;
  content: string;
  parent_comment?: string;
  created_at: string;
  updated_at: string;
}

export interface DirectusUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  role: string;
  bio?: string;
  created_at: string;
}

// Test connection function
export const testConnection = async () => {
  try {
    // Test basic connection by fetching server info
    const serverResponse = await axios.get(`${DIRECTUS_URL}/server/info`);
    console.log('Server info:', serverResponse.data);

    // Test collections endpoint with public token
    const collectionsResponse = await axios.get(`${DIRECTUS_URL}/collections`, {
      headers: {
        'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
        'Content-Type': 'application/json',
      },
    });
    console.log('Collections:', collectionsResponse.data);

    return {
      success: true,
      server: serverResponse.data,
      collections: collectionsResponse.data,
    };
  } catch (error) {
    console.error('Connection test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// API functions
export const api = {
  // Authentication
  login: async (email: string, password: string) => {
    const response = await auth.post('/login', { email, password });
    return response.data;
  },

  register: async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) => {
    const response = await auth.post('/register', userData);
    return response.data;
  },

  // Posts
  getPosts: async (limit = 20, offset = 0) => {
    const response = await directus.get('/posts', {
      params: {
        limit,
        offset,
        sort: '-created_at',
        fields: '*,author.first_name,author.last_name,author.avatar',
      },
    });
    return response.data;
  },

  getPost: async (id: string) => {
    const response = await directus.get(`/posts/${id}`, {
      params: {
        fields: '*,author.first_name,author.last_name,author.avatar',
      },
    });
    return response.data;
  },

  createPost: async (postData: Partial<DirectusPost>) => {
    const response = await directus.post('/posts', postData);
    return response.data;
  },

  updatePost: async (id: string, postData: Partial<DirectusPost>) => {
    const response = await directus.patch(`/posts/${id}`, postData);
    return response.data;
  },

  deletePost: async (id: string) => {
    const response = await directus.delete(`/posts/${id}`);
    return response.data;
  },

  // Comments
  getComments: async (postId: string) => {
    const response = await directus.get('/comments', {
      params: {
        filter: { post: { _eq: postId } },
        sort: 'created_at',
        fields: '*,author.first_name,author.last_name,author.avatar',
      },
    });
    return response.data;
  },

  createComment: async (commentData: Partial<DirectusComment>) => {
    const response = await directus.post('/comments', commentData);
    return response.data;
  },

  // Users
  getUserProfile: async (id: string) => {
    const response = await directus.get(`/directus_users/${id}`);
    return response.data;
  },

  updateUserProfile: async (id: string, userData: Partial<DirectusUser>) => {
    const response = await directus.patch(`/directus_users/${id}`, userData);
    return response.data;
  },
};