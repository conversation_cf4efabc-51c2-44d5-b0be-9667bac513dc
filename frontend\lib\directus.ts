import axios from 'axios';

// Directus configuration
const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
const DIRECTUS_TOKEN = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN;

// Create axios instance for Directus API
export const directus = axios.create({
  baseURL: `${DIRECTUS_URL}/items`,
  headers: {
    'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

// Authentication endpoints
export const auth = axios.create({
  baseURL: `${DIRECTUS_URL}/auth`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for Directus collections based on actual schema
export interface DirectusPost {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Description: string;
  Categories?: DirectusCategory[];
}

export interface DirectusCategory {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Category: string;
}

export interface DirectusEvent {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Description: string;
  Start_date: string;
  End_date: string;
  Event_Categories?: DirectusEventCategory[];
}

export interface DirectusEventCategory {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Category: string;
}

export interface DirectusBannerSlider {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Slider_image: string;
}

export interface DirectusFeature {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title?: string;
  Description?: string;
}

export interface DirectusTestimonial {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Name?: string;
  Content?: string;
}

export interface DirectusSocialMedia {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Platform?: string;
  URL?: string;
}

export interface DirectusUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  role: string;
  status: 'active' | 'invited' | 'draft' | 'suspended' | 'archived';
}

// Test connection function
export const testConnection = async () => {
  try {
    // Test basic connection by fetching server info
    const serverResponse = await axios.get(`${DIRECTUS_URL}/server/info`);
    console.log('Server info:', serverResponse.data);

    // Test actual collections with public token
    const testResults: any = {
      server: serverResponse.data,
      collections: {},
    };

    // Test each collection
    const collectionsToTest = [
      'Posts',
      'Categories',
      'Events',
      'Event_Categories',
      'Banner_Slider',
      'Features',
      'Testimonials',
      'Social_media'
    ];

    for (const collection of collectionsToTest) {
      try {
        const response = await axios.get(`${DIRECTUS_URL}/items/${collection}`, {
          headers: {
            'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
            'Content-Type': 'application/json',
          },
          params: {
            limit: 5, // Just get a few items to test
            fields: 'id,status,Title,Category', // Basic fields
          },
        });
        testResults.collections[collection] = {
          success: true,
          count: response.data.data?.length || 0,
          data: response.data.data || [],
        };
      } catch (collectionError) {
        testResults.collections[collection] = {
          success: false,
          error: collectionError instanceof Error ? collectionError.message : 'Unknown error',
        };
      }
    }

    return {
      success: true,
      ...testResults,
    };
  } catch (error) {
    console.error('Connection test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// API functions
export const api = {
  // Authentication
  login: async (email: string, password: string) => {
    const response = await auth.post('/login', { email, password });
    return response.data;
  },

  register: async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) => {
    const response = await auth.post('/register', userData);
    return response.data;
  },

  // Posts
  getPosts: async (limit = 20, offset = 0, status = 'published') => {
    const response = await directus.get('/Posts', {
      params: {
        limit,
        offset,
        sort: '-date_created',
        filter: { status: { _eq: status } },
        fields: '*,Categories.Category',
      },
    });
    return response.data;
  },

  getPost: async (id: number) => {
    const response = await directus.get(`/Posts/${id}`, {
      params: {
        fields: '*,Categories.Category',
      },
    });
    return response.data;
  },

  createPost: async (postData: Partial<DirectusPost>) => {
    const response = await directus.post('/Posts', postData);
    return response.data;
  },

  updatePost: async (id: number, postData: Partial<DirectusPost>) => {
    const response = await directus.patch(`/Posts/${id}`, postData);
    return response.data;
  },

  deletePost: async (id: number) => {
    const response = await directus.delete(`/Posts/${id}`);
    return response.data;
  },

  // Categories
  getCategories: async (status = 'published') => {
    const response = await directus.get('/Categories', {
      params: {
        filter: { status: { _eq: status } },
        sort: 'Category',
        fields: '*',
      },
    });
    return response.data;
  },

  createCategory: async (categoryData: Partial<DirectusCategory>) => {
    const response = await directus.post('/Categories', categoryData);
    return response.data;
  },

  // Events
  getEvents: async (limit = 20, offset = 0, status = 'published') => {
    const response = await directus.get('/Events', {
      params: {
        limit,
        offset,
        sort: 'Start_date',
        filter: { status: { _eq: status } },
        fields: '*,Event_Categories.Category',
      },
    });
    return response.data;
  },

  getEvent: async (id: number) => {
    const response = await directus.get(`/Events/${id}`, {
      params: {
        fields: '*,Event_Categories.Category',
      },
    });
    return response.data;
  },

  createEvent: async (eventData: Partial<DirectusEvent>) => {
    const response = await directus.post('/Events', eventData);
    return response.data;
  },

  // Banner Sliders
  getBannerSliders: async (status = 'published') => {
    const response = await directus.get('/Banner_Slider', {
      params: {
        filter: { status: { _eq: status } },
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Features
  getFeatures: async (status = 'published') => {
    const response = await directus.get('/Features', {
      params: {
        filter: { status: { _eq: status } },
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Testimonials
  getTestimonials: async (status = 'published') => {
    const response = await directus.get('/Testimonials', {
      params: {
        filter: { status: { _eq: status } },
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Social Media
  getSocialMedia: async (status = 'published') => {
    const response = await directus.get('/Social_media', {
      params: {
        filter: { status: { _eq: status } },
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Users
  getUserProfile: async (id: string) => {
    const response = await directus.get(`/directus_users/${id}`);
    return response.data;
  },

  updateUserProfile: async (id: string, userData: Partial<DirectusUser>) => {
    const response = await directus.patch(`/directus_users/${id}`, userData);
    return response.data;
  },

  // Quick test function to verify all collections are accessible
  testAllCollections: async () => {
    const results: any = {};
    const collections = [
      'Posts',
      'Categories',
      'Events',
      'Event_Categories',
      'Banner_Slider',
      'Features',
      'Testimonials',
      'Social_media'
    ];

    for (const collection of collections) {
      try {
        const response = await directus.get(`/${collection}`, {
          params: { limit: 1 }
        });
        results[collection] = {
          success: true,
          count: response.data.data?.length || 0,
          sample: response.data.data?.[0] || null
        };
      } catch (error) {
        results[collection] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return results;
  },
};