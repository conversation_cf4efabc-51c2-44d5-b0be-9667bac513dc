import axios from 'axios';

// Directus configuration
const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
const DIRECTUS_TOKEN = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN;

// Helper function for Directus API calls with proper URL encoding
const directusFetch = async (collection: string, params: Record<string, string> = {}) => {
  const baseUrl = `${DIRECTUS_URL}/items/${collection}`;
  const queryParams = Object.entries(params)
    .map(([key, value]) => {
      if (key.includes('[') && key.includes(']')) {
        // Handle filter parameters with special encoding
        const encodedKey = key.replace(/\[/g, '%5B').replace(/\]/g, '%5D');
        return `${encodedKey}=${encodeURIComponent(value)}`;
      }
      return `${key}=${encodeURIComponent(value)}`;
    })
    .join('&');

  const url = queryParams ? `${baseUrl}?${queryParams}` : baseUrl;

  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Create axios instance for Directus API (public token)
export const directus = axios.create({
  baseURL: `${DIRECTUS_URL}/items`,
  headers: {
    'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

// Create authenticated axios instance (user token)
export const createAuthenticatedDirectus = (userToken: string) => {
  return axios.create({
    baseURL: `${DIRECTUS_URL}/items`,
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json',
    },
  });
};

// Get authenticated instance from localStorage
export const getAuthenticatedDirectus = () => {
  const token = localStorage.getItem('directus_access_token');
  if (!token) {
    throw new Error('No authentication token found');
  }
  return createAuthenticatedDirectus(token);
};

// Authentication endpoints
export const auth = axios.create({
  baseURL: `${DIRECTUS_URL}/auth`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for Directus collections based on actual schema
export interface DirectusPost {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Description: string;
  Categories?: DirectusCategory[];
}

export interface DirectusCategory {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Category: string;
}

export interface DirectusEvent {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Description: string;
  Start_date: string;
  End_date: string;
  Event_Categories?: DirectusEventCategory[];
}

export interface DirectusEventCategory {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Category: string;
}

export interface DirectusBannerSlider {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title: string;
  Slider_image: string;
}

export interface DirectusFeature {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Title?: string;
  Description?: string;
}

export interface DirectusTestimonial {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Name?: string;
  Content?: string;
}

export interface DirectusSocialMedia {
  id: number;
  status: 'draft' | 'published' | 'archived';
  user_created?: string;
  date_created?: string;
  user_updated?: string;
  date_updated?: string;
  Platform?: string;
  URL?: string;
}

export interface DirectusUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  role: string;
  status: 'active' | 'invited' | 'draft' | 'suspended' | 'archived';
}

// Test connection function
export const testConnection = async () => {
  try {
    // Test basic connection by fetching server info
    const serverResponse = await axios.get(`${DIRECTUS_URL}/server/info`);
    console.log('Server info:', serverResponse.data);

    // Test actual collections with public token
    const testResults: any = {
      server: serverResponse.data,
      collections: {},
    };

    // Test each collection
    const collectionsToTest = [
      'Posts',
      'Categories',
      'Events',
      'Event_Categories',
      'Banner_Slider',
      'Features',
      'Testimonials',
      'Social_media'
    ];

    for (const collection of collectionsToTest) {
      try {
        const response = await axios.get(`${DIRECTUS_URL}/items/${collection}`, {
          headers: {
            'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
            'Content-Type': 'application/json',
          },
          params: {
            limit: 5, // Just get a few items to test
            fields: 'id,status,Title,Category', // Basic fields
          },
        });
        testResults.collections[collection] = {
          success: true,
          count: response.data.data?.length || 0,
          data: response.data.data || [],
        };
      } catch (collectionError) {
        testResults.collections[collection] = {
          success: false,
          error: collectionError instanceof Error ? collectionError.message : 'Unknown error',
        };
      }
    }

    return {
      success: true,
      ...testResults,
    };
  } catch (error) {
    console.error('Connection test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Authentication API functions
export const authAPI = {
  // Login with Directus
  login: async (email: string, password: string) => {
    try {
      const response = await fetch(`${DIRECTUS_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.errors?.[0]?.message || 'Login failed');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Register new user with Writer role
  register: async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) => {
    try {
      // First, get the Writer role ID
      const rolesResponse = await fetch(`${DIRECTUS_URL}/roles?filter[name][_eq]=Writer`, {
        headers: {
          'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
          'Content-Type': 'application/json',
        },
      });

      let writerRoleId = null;
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        writerRoleId = rolesData.data?.[0]?.id;
      }

      // If Writer role not found, we'll let Directus handle the default role
      const userPayload = {
        ...userData,
        status: 'active',
        ...(writerRoleId && { role: writerRoleId }),
      };

      const response = await fetch(`${DIRECTUS_URL}/users`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${DIRECTUS_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userPayload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.errors?.[0]?.message || 'Registration failed');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  // Get current user info
  getCurrentUser: async (token: string) => {
    try {
      const response = await fetch(`${DIRECTUS_URL}/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to get user info');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  },

  // Refresh token
  refresh: async (refreshToken: string) => {
    try {
      const response = await fetch(`${DIRECTUS_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  },

  // Logout
  logout: async (refreshToken: string) => {
    try {
      await fetch(`${DIRECTUS_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Don't throw error for logout, just log it
    }
  },
};

// API functions
export const api = {

  // Posts
  getPosts: async (limit = 20, offset = 0, status = 'published') => {
    const response = await directus.get('/Posts', {
      params: {
        limit,
        offset,
        sort: '-date_created',
        'filter[status][_eq]': status,
        fields: '*,Categories.Category',
      },
    });
    return response.data;
  },

  getPost: async (id: number) => {
    const response = await directus.get(`/Posts/${id}`, {
      params: {
        fields: '*,Categories.Category',
      },
    });
    return response.data;
  },

  createPost: async (postData: Partial<DirectusPost>) => {
    const response = await directus.post('/Posts', postData);
    return response.data;
  },

  updatePost: async (id: number, postData: Partial<DirectusPost>) => {
    const response = await directus.patch(`/Posts/${id}`, postData);
    return response.data;
  },

  deletePost: async (id: number) => {
    const response = await directus.delete(`/Posts/${id}`);
    return response.data;
  },

  // Categories
  getCategories: async (status = 'published') => {
    const response = await directus.get('/Categories', {
      params: {
        'filter[status][_eq]': status,
        sort: 'Category',
        fields: '*',
      },
    });
    return response.data;
  },

  createCategory: async (categoryData: Partial<DirectusCategory>) => {
    const response = await directus.post('/Categories', categoryData);
    return response.data;
  },

  // Events
  getEvents: async (limit = 20, offset = 0, status = 'published') => {
    const response = await directus.get('/Events', {
      params: {
        limit,
        offset,
        sort: 'Start_date',
        'filter[status][_eq]': status,
        fields: '*,Event_Categories.Category',
      },
    });
    return response.data;
  },

  getEvent: async (id: number) => {
    const response = await directus.get(`/Events/${id}`, {
      params: {
        fields: '*,Event_Categories.Category',
      },
    });
    return response.data;
  },

  createEvent: async (eventData: Partial<DirectusEvent>) => {
    const response = await directus.post('/Events', eventData);
    return response.data;
  },

  // Banner Sliders
  getBannerSliders: async (status = 'published') => {
    try {
      const data = await directusFetch('Banner_Slider', {
        'filter[status][_eq]': status,
        'sort': 'id',
        'fields': '*'
      });
      console.log('Banner sliders response:', data);
      return data;
    } catch (error) {
      console.error('Error fetching banner sliders:', error);
      throw error;
    }
  },

  // Features
  getFeatures: async (status = 'published') => {
    const response = await directus.get('/Features', {
      params: {
        'filter[status][_eq]': status,
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Testimonials
  getTestimonials: async (status = 'published') => {
    const response = await directus.get('/Testimonials', {
      params: {
        'filter[status][_eq]': status,
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Social Media
  getSocialMedia: async (status = 'published') => {
    const response = await directus.get('/Social_media', {
      params: {
        'filter[status][_eq]': status,
        sort: 'id',
        fields: '*',
      },
    });
    return response.data;
  },

  // Users
  getUserProfile: async (id: string) => {
    const response = await directus.get(`/directus_users/${id}`);
    return response.data;
  },

  updateUserProfile: async (id: string, userData: Partial<DirectusUser>) => {
    const response = await directus.patch(`/directus_users/${id}`, userData);
    return response.data;
  },

  // Quick test function to verify all collections are accessible
  testAllCollections: async () => {
    const results: any = {};
    const collections = [
      'Posts',
      'Categories',
      'Events',
      'Event_Categories',
      'Banner_Slider',
      'Features',
      'Testimonials',
      'Social_media'
    ];

    for (const collection of collections) {
      try {
        const response = await directus.get(`/${collection}`, {
          params: { limit: 1 }
        });
        results[collection] = {
          success: true,
          count: response.data.data?.length || 0,
          sample: response.data.data?.[0] || null
        };
      } catch (error) {
        results[collection] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return results;
  },
};

// Authenticated API functions (require user login)
export const authenticatedAPI = {
  // Create a new post (requires authentication)
  createPost: async (postData: {
    Title: string;
    Description: string;
    Categories?: number[];
  }) => {
    try {
      const authDirectus = getAuthenticatedDirectus();
      const response = await authDirectus.post('/Posts', {
        ...postData,
        status: 'draft', // Start as draft
      });
      return response.data;
    } catch (error) {
      console.error('Create post error:', error);
      throw error;
    }
  },

  // Update user's own post
  updatePost: async (id: number, postData: Partial<DirectusPost>) => {
    try {
      const authDirectus = getAuthenticatedDirectus();
      const response = await authDirectus.patch(`/Posts/${id}`, postData);
      return response.data;
    } catch (error) {
      console.error('Update post error:', error);
      throw error;
    }
  },

  // Get user's own posts
  getUserPosts: async (limit = 20, offset = 0) => {
    try {
      const authDirectus = getAuthenticatedDirectus();
      const response = await authDirectus.get('/Posts', {
        params: {
          limit,
          offset,
          sort: '-date_created',
          'filter[user_created][_eq]': '$CURRENT_USER',
          fields: '*,Categories.Category',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Get user posts error:', error);
      throw error;
    }
  },

  // Update user profile
  updateProfile: async (profileData: {
    first_name?: string;
    last_name?: string;
    avatar?: string;
  }) => {
    try {
      const authDirectus = getAuthenticatedDirectus();
      const response = await authDirectus.patch('/users/me', profileData);
      return response.data;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  },
};