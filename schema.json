{"collections": [{"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": "Website", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Community", "meta": {"collection": "Community", "icon": "folder", "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": null}, {"collection": "Events", "meta": {"collection": "Events", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": "Event_Manager", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Posts", "meta": {"collection": "Posts", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Categories", "meta": {"collection": "Categories", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 2, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Event_Categories", "meta": {"collection": "Event_Categories", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 2, "group": "Event_Manager", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Event_Manager", "meta": {"collection": "Event_Manager", "icon": "folder", "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 2, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": null}, {"collection": "Features", "meta": {"collection": "Features", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 2, "group": "Website", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Event_Categories_Events", "meta": {"collection": "Event_Categories_Events", "icon": "import_export", "note": null, "display_template": null, "hidden": true, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 3, "group": "Event_Manager", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Testimonials", "meta": {"collection": "Testimonials", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 3, "group": "Website", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Website", "meta": {"collection": "Website", "icon": "folder", "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 3, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": null}, {"collection": "comments", "meta": {"collection": "comments", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 3, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Categories_Posts", "meta": {"collection": "Categories_Posts", "icon": "import_export", "note": null, "display_template": null, "hidden": true, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 4, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Social_media", "meta": {"collection": "Social_media", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 4, "group": "Website", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Posts_comments", "meta": {"collection": "Posts_comments", "icon": "import_export", "note": null, "display_template": null, "hidden": true, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 5, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}], "fields": [{"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "id", "type": "integer", "schema": {"name": "id", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "integer", "default_value": "nextval('\"Banner_Slider_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "status", "type": "string", "schema": {"name": "status", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Title", "type": "string", "schema": {"name": "Title", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Slider_image", "type": "uuid", "schema": {"name": "Slider_image", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Slider_image", "special": ["file"], "interface": "file-image", "options": {"crop": false}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Events", "data_type": "integer", "default_value": "nextval('\"Events_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "status", "type": "string", "schema": {"name": "status", "table": "Events", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Events", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Events", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Events", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Events", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Events", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Events", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "Title", "type": "string", "schema": {"name": "Title", "table": "Events", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "Title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "Description", "type": "text", "schema": {"name": "Description", "table": "Events", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "Description", "special": null, "interface": "input-rich-text-html", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "Start_date", "type": "dateTime", "schema": {"name": "Start_date", "table": "Events", "data_type": "timestamp without time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "Start_date", "special": null, "interface": "datetime", "options": {}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "End_date", "type": "dateTime", "schema": {"name": "End_date", "table": "Events", "data_type": "timestamp without time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Events", "field": "End_date", "special": null, "interface": "datetime", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "half", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Events", "field": "Event_Categories", "type": "alias", "schema": null, "meta": {"collection": "Events", "field": "Event_Categories", "special": ["m2m"], "interface": "list-m2m", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Posts", "data_type": "integer", "default_value": "nextval('\"Posts_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "status", "type": "string", "schema": {"name": "status", "table": "Posts", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Categories", "type": "alias", "schema": null, "meta": {"collection": "Posts", "field": "Categories", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{Category}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Is_Public", "type": "boolean", "schema": {"name": "Is_Public", "table": "Posts", "data_type": "boolean", "default_value": false, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Is_Public", "special": ["cast-boolean"], "interface": "boolean", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Title", "type": "string", "schema": {"name": "Title", "table": "Posts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Description", "type": "text", "schema": {"name": "Description", "table": "Posts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Description", "special": null, "interface": "input-rich-text-html", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Tags", "type": "json", "schema": {"name": "Tags", "table": "Posts", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Tags", "special": ["cast-json"], "interface": "tags", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "comments", "type": "alias", "schema": null, "meta": {"collection": "Posts", "field": "comments", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{comment}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user", "type": "uuid", "schema": {"name": "user", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{first_name}} {{last_name}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 13, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Categories", "data_type": "integer", "default_value": "nextval('\"Categories_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "status", "type": "string", "schema": {"name": "status", "table": "Categories", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "Category", "type": "string", "schema": {"name": "Category", "table": "Categories", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "Category", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "Posts", "type": "integer", "schema": {"name": "Posts", "table": "Categories", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories", "field": "Posts", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{Title}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Event_Categories", "data_type": "integer", "default_value": "nextval('\"Event_Categories_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "status", "type": "string", "schema": {"name": "status", "table": "Event_Categories", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Event_Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Event_Categories", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Event_Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Event_Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Event_Categories", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Event_Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "Title", "type": "string", "schema": {"name": "Title", "table": "Event_Categories", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories", "field": "Title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories", "field": "Event_categories", "type": "alias", "schema": null, "meta": {"collection": "Event_Categories", "field": "Event_categories", "special": ["m2m"], "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Features", "data_type": "integer", "default_value": "nextval('\"Features_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "status", "type": "string", "schema": {"name": "status", "table": "Features", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Features", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Features", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Features", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Features", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Features", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Features", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "title", "type": "string", "schema": {"name": "title", "table": "Features", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "icon", "type": "string", "schema": {"name": "icon", "table": "Features", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "icon", "special": null, "interface": "select-icon", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Features", "field": "description", "type": "text", "schema": {"name": "description", "table": "Features", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": true, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Features", "field": "description", "special": null, "interface": "input-multiline", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories_Events", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Event_Categories_Events", "data_type": "integer", "default_value": "nextval('\"Event_Categories_Events_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Event_Categories_Events", "field": "id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories_Events", "field": "Event_Categories_id", "type": "integer", "schema": {"name": "Event_Categories_id", "table": "Event_Categories_Events", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Event_Categories", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Event_Categories_Events", "field": "Event_Categories_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Event_Categories_Events", "field": "Events_id", "type": "integer", "schema": {"name": "Events_id", "table": "Event_Categories_Events", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Events", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Event_Categories_Events", "field": "Events_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Testimonials", "data_type": "integer", "default_value": "nextval('\"Testimonials_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "status", "type": "string", "schema": {"name": "status", "table": "Testimonials", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Testimonials", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Testimonials", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Testimonials", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Testimonials", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Testimonials", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Testimonials", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "title", "type": "string", "schema": {"name": "title", "table": "Testimonials", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "rating", "type": "integer", "schema": {"name": "rating", "table": "Testimonials", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Testimonials", "field": "rating", "special": null, "interface": "slider", "options": {"minValue": 0, "maxValue": 5, "stepInterval": 1, "alwaysShowValue": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Testimonials", "field": "user", "type": "uuid", "schema": {"name": "user", "table": "Testimonials", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Testimonials", "field": "user", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{first_name}} {{last_name}}", "enableCreate": false}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "id", "type": "integer", "schema": {"name": "id", "table": "comments", "data_type": "integer", "default_value": "nextval('comments_id_seq'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "comments", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "status", "type": "string", "schema": {"name": "status", "table": "comments", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "comments", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "comments", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "comments", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "comments", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "comments", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "comments", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "comments", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "comments", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "comments", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "comment", "type": "text", "schema": {"name": "comment", "table": "comments", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "comments", "field": "comment", "special": null, "interface": "input-multiline", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "post", "type": "integer", "schema": {"name": "post", "table": "comments", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "comments", "field": "post", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"enableCreate": false, "template": "{{Title}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "comments", "field": "user", "type": "uuid", "schema": {"name": "user", "table": "comments", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "comments", "field": "user", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{first_name}} {{last_name}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Categories_Posts", "data_type": "integer", "default_value": "nextval('\"Categories_Posts_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories_Posts", "field": "id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "Categories_id", "type": "integer", "schema": {"name": "Categories_id", "table": "Categories_Posts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Categories", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories_Posts", "field": "Categories_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "Posts_id", "type": "integer", "schema": {"name": "Posts_id", "table": "Categories_Posts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories_Posts", "field": "Posts_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Social_media", "data_type": "integer", "default_value": "nextval('\"Social_media_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "status", "type": "string", "schema": {"name": "status", "table": "Social_media", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Social_media", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Social_media", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Social_media", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Social_media", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Social_media", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Social_media", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "icon", "type": "string", "schema": {"name": "icon", "table": "Social_media", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "icon", "special": null, "interface": "select-icon", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "title", "type": "string", "schema": {"name": "title", "table": "Social_media", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Social_media", "field": "link", "type": "string", "schema": {"name": "link", "table": "Social_media", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Social_media", "field": "link", "special": null, "interface": "input", "options": {"placeholder": "https://"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_comments", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Posts_comments", "data_type": "integer", "default_value": "nextval('\"Posts_comments_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts_comments", "field": "id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_comments", "field": "Posts_id", "type": "integer", "schema": {"name": "Posts_id", "table": "Posts_comments", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts_comments", "field": "Posts_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_comments", "field": "comments_id", "type": "integer", "schema": {"name": "comments_id", "table": "Posts_comments", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "comments", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts_comments", "field": "comments_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}], "relations": [{"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "banner_slider_user_updated_foreign", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "banner_slider_user_created_foreign", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field": "Slider_image", "related_collection": "directus_files", "schema": {"constraint_name": "banner_slider_slider_image_foreign", "table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "column": "Slider_image", "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "many_field": "Slider_image", "one_collection": "directus_files", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Events", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "events_user_updated_foreign", "table": "Events", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Events", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Events", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "events_user_created_foreign", "table": "Events", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Events", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts", "field": "user", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_foreign", "table": "Posts", "column": "user", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Posts", "many_field": "user", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_updated_foreign", "table": "Posts", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Posts", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_created_foreign", "table": "Posts", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Posts", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "categories_user_created_foreign", "table": "Categories", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Categories", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "categories_user_updated_foreign", "table": "Categories", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Categories", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories", "field": "Posts", "related_collection": "Posts", "schema": {"constraint_name": "categories_posts_foreign", "table": "Categories", "column": "Posts", "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Categories", "many_field": "Posts", "one_collection": "Posts", "one_field": "Categories", "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Event_Categories", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "event_categories_user_updated_foreign", "table": "Event_Categories", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Event_Categories", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Event_Categories", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "event_categories_user_created_foreign", "table": "Event_Categories", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Event_Categories", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Features", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "features_user_created_foreign", "table": "Features", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Features", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Features", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "features_user_updated_foreign", "table": "Features", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Features", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Event_Categories_Events", "field": "Events_id", "related_collection": "Events", "schema": {"constraint_name": "event_categories_events_events_id_foreign", "table": "Event_Categories_Events", "column": "Events_id", "foreign_key_schema": "public", "foreign_key_table": "Events", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Event_Categories_Events", "many_field": "Events_id", "one_collection": "Events", "one_field": "Event_Categories", "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Event_Categories_id", "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Event_Categories_Events", "field": "Event_Categories_id", "related_collection": "Event_Categories", "schema": {"constraint_name": "event_categories_events_event_categories_id_foreign", "table": "Event_Categories_Events", "column": "Event_Categories_id", "foreign_key_schema": "public", "foreign_key_table": "Event_Categories", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Event_Categories_Events", "many_field": "Event_Categories_id", "one_collection": "Event_Categories", "one_field": "Event_categories", "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Events_id", "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Testimonials", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "testimonials_user_created_foreign", "table": "Testimonials", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Testimonials", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Testimonials", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "testimonials_user_updated_foreign", "table": "Testimonials", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Testimonials", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Testimonials", "field": "user", "related_collection": "directus_users", "schema": {"constraint_name": "testimonials_user_foreign", "table": "Testimonials", "column": "user", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Testimonials", "many_field": "user", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "comments", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "comments_user_updated_foreign", "table": "comments", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "comments", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "comments", "field": "user", "related_collection": "directus_users", "schema": {"constraint_name": "comments_user_foreign", "table": "comments", "column": "user", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "comments", "many_field": "user", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "comments", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "comments_user_created_foreign", "table": "comments", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "comments", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "comments", "field": "post", "related_collection": "Posts", "schema": {"constraint_name": "comments_post_foreign", "table": "comments", "column": "post", "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "comments", "many_field": "post", "one_collection": "Posts", "one_field": "comments", "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Social_media", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "social_media_user_updated_foreign", "table": "Social_media", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Social_media", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Social_media", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "social_media_user_created_foreign", "table": "Social_media", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Social_media", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts_comments", "field": "comments_id", "related_collection": "comments", "schema": {"constraint_name": "posts_comments_comments_id_foreign", "table": "Posts_comments", "column": "comments_id", "foreign_key_schema": "public", "foreign_key_table": "comments", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Posts_comments", "many_field": "comments_id", "one_collection": "comments", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Posts_id", "sort_field": null, "one_deselect_action": "nullify"}}]}