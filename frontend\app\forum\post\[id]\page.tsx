'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Header } from '@/components/header';
import { Sidebar } from '@/components/sidebar';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import {
  Heart,
  MessageCircle,
  Share2,
  ArrowLeft,
  Send
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { api, DirectusPost, DirectusComment, authenticatedAPI } from '@/lib/directus';
import { useAuth } from '@/components/auth-provider';

// Helper function to generate dummy avatar with first letter
const generateDummyAvatar = (firstName: string) => {
  const letter = firstName?.charAt(0)?.toUpperCase() || 'U';
  const colors = [
    'bg-gradient-to-br from-orange-400 to-red-500',
    'bg-gradient-to-br from-amber-400 to-orange-500',
    'bg-gradient-to-br from-red-400 to-pink-500',
    'bg-gradient-to-br from-yellow-400 to-amber-500',
    'bg-gradient-to-br from-pink-400 to-red-500',
    'bg-gradient-to-br from-purple-400 to-pink-500',
    'bg-gradient-to-br from-blue-400 to-purple-500',
    'bg-gradient-to-br from-green-400 to-blue-500',
  ];
  
  const colorIndex = letter.charCodeAt(0) % colors.length;
  const gradientClass = colors[colorIndex];
  
  return (
    <div className={`w-10 h-10 rounded-full ${gradientClass} flex items-center justify-center text-white font-bold text-sm shadow-lg`}>
      {letter}
    </div>
  );
};

// Helper function to get avatar URL from Directus
const getAvatarUrl = (avatarId: string) => {
  const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
  return `${directusUrl}/assets/${avatarId}`;
};

export default function PostDetailPage() {
  const params = useParams();
  const postId = params.id as string;
  const { user, isAuthenticated } = useAuth();

  const [post, setPost] = useState<DirectusPost | null>(null);
  const [comments, setComments] = useState<DirectusComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);

  useEffect(() => {
    const fetchPostAndComments = async () => {
      try {
        setLoading(true);
        
        // Fetch post details
        const postResponse = await api.getPost(parseInt(postId));
        if (postResponse && postResponse.data) {
          setPost(postResponse.data);
        }
        
        // Fetch comments for this post
        const commentsResponse = await api.getComments(parseInt(postId));
        if (commentsResponse && commentsResponse.data) {
          setComments(commentsResponse.data);
        }
      } catch (err) {
        console.error('Error fetching post details:', err);
        setError('Failed to load post details');
      } finally {
        setLoading(false);
      }
    };

    if (postId) {
      fetchPostAndComments();
    }
  }, [postId]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !isAuthenticated) return;

    try {
      setSubmittingComment(true);

      // Use authenticated API to create comment
      const authDirectus = await import('@/lib/directus').then(m => m.getAuthenticatedDirectus());
      await authDirectus.post('/comments', {
        comment: newComment, // Updated field name
        post: parseInt(postId),
        user: user?.id, // Set the user explicitly
        status: 'published'
      });

      // Refresh comments
      const commentsResponse = await api.getComments(parseInt(postId));
      if (commentsResponse && commentsResponse.data) {
        setComments(commentsResponse.data);
      }

      setNewComment('');
    } catch (err) {
      console.error('Error submitting comment:', err);
      alert('Failed to submit comment. Please try again.');
    } finally {
      setSubmittingComment(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex">
          <Sidebar />
          <main className="flex-1 lg:ml-64">
            <div className="container mx-auto px-4 py-6 max-w-4xl">
              <div className="animate-pulse space-y-6">
                <div className="h-8 bg-gray-300 rounded w-1/4"></div>
                <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-300 rounded w-32"></div>
                        <div className="h-3 bg-gray-300 rounded w-24"></div>
                      </div>
                    </div>
                    <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 rounded w-full"></div>
                      <div className="h-4 bg-gray-300 rounded w-2/3"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex">
          <Sidebar />
          <main className="flex-1 lg:ml-64">
            <div className="container mx-auto px-4 py-6 max-w-4xl">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
                <p className="text-red-600 dark:text-red-400">{error || 'Post not found'}</p>
                <Link href="/forum" className="mt-2 text-red-600 dark:text-red-400 underline hover:no-underline">
                  Back to Forum
                </Link>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  const user = typeof post.user === 'object' ? post.user : null;
  const userName = user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() : 'Anonymous User';
  const firstName = user?.first_name || 'Anonymous';

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 lg:ml-64">
          <div className="container mx-auto px-4 py-6 max-w-4xl">
            {/* Back button */}
            <Link href="/forum" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Forum
            </Link>

            {/* Post content */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    {user?.avatar ? (
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={getAvatarUrl(user.avatar)} alt={userName} />
                        <AvatarFallback>{firstName.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    ) : (
                      generateDummyAvatar(firstName)
                    )}
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-foreground">{userName}</p>
                        <Badge variant="outline" className="text-xs">
                          Community Member
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        <p className="text-sm text-muted-foreground">
                          {formatDistanceToNow(new Date(post.date_created || ''), { addSuffix: true })}
                        </p>
                        {post.Categories?.[0] && (
                          <>
                            <span className="text-sm text-muted-foreground">•</span>
                            <Badge variant="secondary" className="text-xs">
                              {post.Categories[0].Category}
                            </Badge>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <h1 className="text-2xl font-bold text-foreground mb-4">{post.Title}</h1>
                <div 
                  className="prose prose-gray dark:prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: post.Description }}
                />
                
                {post.Tags && post.Tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-6">
                    {post.Tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                )}

                <div className="flex items-center space-x-4 mt-6 pt-4 border-t">
                  <Button variant="ghost" size="sm" className="space-x-2">
                    <Heart className="h-4 w-4" />
                    <span className="text-sm">0</span>
                  </Button>
                  
                  <Button variant="ghost" size="sm" className="space-x-2">
                    <MessageCircle className="h-4 w-4" />
                    <span className="text-sm">{comments.length}</span>
                  </Button>

                  <Button variant="ghost" size="sm">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Comments section */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Comments ({comments.length})</h2>
              
              {/* Comment form */}
              <Card>
                <CardContent className="pt-6">
                  {isAuthenticated ? (
                    <form onSubmit={handleSubmitComment} className="space-y-4">
                      <Textarea
                        placeholder="Write a comment..."
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        className="min-h-[100px]"
                      />
                      <div className="flex justify-end">
                        <Button type="submit" disabled={submittingComment || !newComment.trim()}>
                          <Send className="h-4 w-4 mr-2" />
                          {submittingComment ? 'Posting...' : 'Post Comment'}
                        </Button>
                      </div>
                    </form>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground mb-4">You need to be logged in to post a comment.</p>
                      <Button onClick={() => window.location.href = '/auth-test'}>
                        Sign In
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Comments list */}
              {comments.length === 0 ? (
                <Card>
                  <CardContent className="pt-6 text-center">
                    <p className="text-muted-foreground">No comments yet. Be the first to comment!</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {comments.map((comment) => {
                    const commentUser = typeof comment.user === 'object' ? comment.user : null;
                    const commentUserName = commentUser ? `${commentUser.first_name || ''} ${commentUser.last_name || ''}`.trim() : 'Anonymous User';
                    const commentFirstName = commentUser?.first_name || 'Anonymous';

                    return (
                      <Card key={comment.id}>
                        <CardContent className="pt-6">
                          <div className="flex items-start space-x-3">
                            {commentUser?.avatar ? (
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={getAvatarUrl(commentUser.avatar)} alt={commentUserName} />
                                <AvatarFallback>{commentFirstName.charAt(0).toUpperCase()}</AvatarFallback>
                              </Avatar>
                            ) : (
                              <div className="w-8 h-8">
                                {generateDummyAvatar(commentFirstName)}
                              </div>
                            )}
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <p className="font-medium text-sm">{commentUserName}</p>
                                <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(comment.date_created || ''), { addSuffix: true })}
                                </p>
                              </div>
                              <p className="text-sm text-muted-foreground">{comment.comment}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
