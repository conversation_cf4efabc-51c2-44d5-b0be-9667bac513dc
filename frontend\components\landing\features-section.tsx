'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { 
  MessageCircle, 
  BookOpen, 
  Users, 
  Calendar, 
  Heart, 
  Lightbulb,
  Music,
  Globe
} from 'lucide-react';

const features = [
  {
    icon: MessageCircle,
    title: 'Sacred Discussions',
    description: 'Engage in meaningful conversations about scriptures, philosophy, and spiritual experiences with fellow devotees.',
    gradient: 'from-orange-500 to-red-500'
  },
  {
    icon: BookOpen,
    title: 'Scripture Study',
    description: 'Explore and discuss ancient texts like the Bhagavad Gita, Ramayana, and Upanishads with guided interpretations.',
    gradient: 'from-amber-500 to-orange-500'
  },
  {
    icon: Users,
    title: 'Spiritual Mentorship',
    description: 'Connect with experienced practitioners and spiritual guides who can help you on your devotional journey.',
    gradient: 'from-red-500 to-pink-500'
  },
  {
    icon: Calendar,
    title: 'Festival Calendar',
    description: 'Stay updated with important Hindu festivals, fasting days, and auspicious occasions with detailed explanations.',
    gradient: 'from-yellow-500 to-amber-500'
  },
  {
    icon: Heart,
    title: 'Prayer Circles',
    description: 'Join group prayers and meditation sessions, sharing intentions and supporting each other spiritually.',
    gradient: 'from-pink-500 to-red-500'
  },
  {
    icon: Lightbulb,
    title: 'Daily Wisdom',
    description: 'Receive daily verses, teachings, and spiritual insights to inspire and guide your daily practice.',
    gradient: 'from-orange-500 to-yellow-500'
  },
  {
    icon: Music,
    title: 'Devotional Music',
    description: 'Share and discover beautiful bhajans, kirtans, and devotional songs that elevate the soul.',
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    icon: Globe,
    title: 'Global Community',
    description: 'Connect with Hindu devotees from around the world, sharing diverse traditions and practices.',
    gradient: 'from-blue-500 to-purple-500'
  }
];

export function FeaturesSection() {
  return (
    <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Spiritual Features
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Discover tools and spaces designed to nurture your spiritual growth and connect you with a community of like-minded souls
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 dark:bg-black/20 backdrop-blur-sm hover:scale-105"
            >
              <CardHeader className="text-center pb-4">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${feature.gradient} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-lg font-semibold text-foreground group-hover:text-orange-600 transition-colors">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sacred symbols decoration */}
        <div className="flex justify-center items-center mt-16 space-x-8 opacity-30">
          <span className="text-4xl text-orange-500">🕉️</span>
          <span className="text-4xl text-red-500">🪷</span>
          <span className="text-4xl text-amber-500">🔱</span>
          <span className="text-4xl text-pink-500">🪔</span>
        </div>
      </div>
    </section>
  );
}