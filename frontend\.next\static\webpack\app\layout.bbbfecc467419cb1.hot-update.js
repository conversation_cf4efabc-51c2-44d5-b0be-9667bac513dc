"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/directus.ts":
/*!*************************!*\
  !*** ./lib/directus.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   authenticatedAPI: function() { return /* binding */ authenticatedAPI; },\n/* harmony export */   createAuthenticatedDirectus: function() { return /* binding */ createAuthenticatedDirectus; },\n/* harmony export */   directus: function() { return /* binding */ directus; },\n/* harmony export */   getAuthenticatedDirectus: function() { return /* binding */ getAuthenticatedDirectus; },\n/* harmony export */   testConnection: function() { return /* binding */ testConnection; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Directus configuration\nconst DIRECTUS_URL = \"http://localhost:8055\" || 0;\nconst DIRECTUS_TOKEN = \"wHf_Vc3dv3UslEFsPhjflRI__tU_Xkx0\";\n// Helper function for Directus API calls with proper URL encoding\nconst directusFetch = async function(collection) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const baseUrl = \"\".concat(DIRECTUS_URL, \"/items/\").concat(collection);\n    const queryParams = Object.entries(params).map((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"[\") && key.includes(\"]\")) {\n            // Handle filter parameters with special encoding\n            const encodedKey = key.replace(/\\[/g, \"%5B\").replace(/\\]/g, \"%5D\");\n            return \"\".concat(encodedKey, \"=\").concat(encodeURIComponent(value));\n        }\n        return \"\".concat(key, \"=\").concat(encodeURIComponent(value));\n    }).join(\"&\");\n    const url = queryParams ? \"\".concat(baseUrl, \"?\").concat(queryParams) : baseUrl;\n    const response = await fetch(url, {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n};\n// Create axios instance for Directus API (public token)\nconst directus = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n    headers: {\n        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Create authenticated axios instance (user token)\nconst createAuthenticatedDirectus = (userToken)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n        headers: {\n            \"Authorization\": \"Bearer \".concat(userToken),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Get authenticated instance from localStorage\nconst getAuthenticatedDirectus = ()=>{\n    const token = localStorage.getItem(\"directus_access_token\");\n    if (!token) {\n        throw new Error(\"No authentication token found\");\n    }\n    return createAuthenticatedDirectus(token);\n};\n// Authentication endpoints\nconst auth = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/auth\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Test connection function\nconst testConnection = async ()=>{\n    try {\n        // Test basic connection by fetching server info\n        const serverResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/server/info\"));\n        console.log(\"Server info:\", serverResponse.data);\n        // Test actual collections with public token\n        const testResults = {\n            server: serverResponse.data,\n            collections: {}\n        };\n        // Test each collection\n        const collectionsToTest = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collectionsToTest){\n            try {\n                var _response_data_data;\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/items/\").concat(collection), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    params: {\n                        limit: 5,\n                        fields: \"id,status,Title,Category\"\n                    }\n                });\n                testResults.collections[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    data: response.data.data || []\n                };\n            } catch (collectionError) {\n                testResults.collections[collection] = {\n                    success: false,\n                    error: collectionError instanceof Error ? collectionError.message : \"Unknown error\"\n                };\n            }\n        }\n        return {\n            success: true,\n            ...testResults\n        };\n    } catch (error) {\n        console.error(\"Connection test failed:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n// Authentication API functions\nconst authAPI = {\n    // Login with Directus\n    login: async (email, password)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Login failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    },\n    // Register new user with Writer role\n    register: async (userData)=>{\n        try {\n            // First, get the Writer role ID\n            const rolesResponse = await fetch(\"\".concat(DIRECTUS_URL, \"/roles?filter[name][_eq]=Writer\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            let writerRoleId = null;\n            if (rolesResponse.ok) {\n                var _rolesData_data_, _rolesData_data;\n                const rolesData = await rolesResponse.json();\n                writerRoleId = (_rolesData_data = rolesData.data) === null || _rolesData_data === void 0 ? void 0 : (_rolesData_data_ = _rolesData_data[0]) === null || _rolesData_data_ === void 0 ? void 0 : _rolesData_data_.id;\n            }\n            // If Writer role not found, we'll let Directus handle the default role\n            const userPayload = {\n                ...userData,\n                status: \"active\",\n                ...writerRoleId && {\n                    role: writerRoleId\n                }\n            };\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userPayload)\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Registration failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    },\n    // Get current user info\n    getCurrentUser: async (token)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users/me\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get user info\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            throw error;\n        }\n    },\n    // Refresh token\n    refresh: async (refreshToken)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to refresh token\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            throw error;\n        }\n    },\n    // Logout\n    logout: async (refreshToken)=>{\n        try {\n            await fetch(\"\".concat(DIRECTUS_URL, \"/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        // Don't throw error for logout, just log it\n        }\n    }\n};\n// API functions\nconst api = {\n    // Posts\n    getPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Posts\", {\n            params: {\n                limit,\n                offset,\n                sort: \"-date_created\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Categories.Category,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    getPost: async (id)=>{\n        const response = await directus.get(\"/Posts/\".concat(id), {\n            params: {\n                fields: \"*,Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createPost: async (postData)=>{\n        const response = await directus.post(\"/Posts\", postData);\n        return response.data;\n    },\n    updatePost: async (id, postData)=>{\n        const response = await directus.patch(\"/Posts/\".concat(id), postData);\n        return response.data;\n    },\n    deletePost: async (id)=>{\n        const response = await directus.delete(\"/Posts/\".concat(id));\n        return response.data;\n    },\n    // Categories\n    getCategories: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Categories\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"Category\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    createCategory: async (categoryData)=>{\n        const response = await directus.post(\"/Categories\", categoryData);\n        return response.data;\n    },\n    // Events\n    getEvents: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Events\", {\n            params: {\n                limit,\n                offset,\n                sort: \"Start_date\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    getEvent: async (id)=>{\n        const response = await directus.get(\"/Events/\".concat(id), {\n            params: {\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createEvent: async (eventData)=>{\n        const response = await directus.post(\"/Events\", eventData);\n        return response.data;\n    },\n    // Banner Sliders\n    getBannerSliders: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        try {\n            const data = await directusFetch(\"Banner_Slider\", {\n                \"filter[status][_eq]\": status,\n                \"sort\": \"id\",\n                \"fields\": \"*\"\n            });\n            console.log(\"Banner sliders response:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching banner sliders:\", error);\n            throw error;\n        }\n    },\n    // Features\n    getFeatures: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Features\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Testimonials\n    getTestimonials: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Testimonials\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    // Social Media\n    getSocialMedia: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Social_media\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Users\n    getUserProfile: async (id)=>{\n        const response = await directus.get(\"/directus_users/\".concat(id));\n        return response.data;\n    },\n    updateUserProfile: async (id, userData)=>{\n        const response = await directus.patch(\"/directus_users/\".concat(id), userData);\n        return response.data;\n    },\n    // Quick test function to verify all collections are accessible\n    testAllCollections: async ()=>{\n        const results = {};\n        const collections = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collections){\n            try {\n                var _response_data_data, _response_data_data1;\n                const response = await directus.get(\"/\".concat(collection), {\n                    params: {\n                        limit: 1\n                    }\n                });\n                results[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    sample: ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1[0]) || null\n                };\n            } catch (error) {\n                results[collection] = {\n                    success: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }\n        return results;\n    }\n};\n// Authenticated API functions (require user login)\nconst authenticatedAPI = {\n    // Create a new post (requires authentication)\n    createPost: async (postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.post(\"/Posts\", {\n                ...postData,\n                status: \"draft\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Create post error:\", error);\n            throw error;\n        }\n    },\n    // Update user's own post\n    updatePost: async (id, postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/Posts/\".concat(id), postData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update post error:\", error);\n            throw error;\n        }\n    },\n    // Get user's own posts\n    getUserPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.get(\"/Posts\", {\n                params: {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[user_created][_eq]\": \"$CURRENT_USER\",\n                    fields: \"*,Categories.Category\"\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Get user posts error:\", error);\n            throw error;\n        }\n    },\n    // Update user profile\n    updateProfile: async (profileData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/users/me\", profileData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/directus.ts\n"));

/***/ })

});