'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    location: 'Mumbai, India',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'This community has transformed my spiritual practice. The daily discussions and shared prayers have brought so much peace and clarity to my life.',
    rating: 5
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    location: 'London, UK',
    avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'Being away from home, this platform helps me stay connected to my roots and faith. The festival celebrations here feel just like being with family.',
    rating: 5
  },
  {
    id: 3,
    name: '<PERSON>',
    location: 'Chennai, India',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'The scripture study groups have deepened my understanding of our ancient wisdom. The mentors here are truly knowledgeable and compassionate.',
    rating: 5
  },
  {
    id: 4,
    name: 'Arjun Mehta',
    location: 'California, USA',
    avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'As a young professional, finding time for spirituality was challenging. This community makes it easy to incorporate daily prayers and learning into my busy schedule.',
    rating: 5
  },
  {
    id: 5,
    name: 'Radha Sharma',
    location: 'Delhi, India',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'The prayer circles during difficult times have been a source of immense strength. This community truly embodies the spirit of seva and compassion.',
    rating: 5
  },
  {
    id: 6,
    name: 'Suresh Gupta',
    location: 'Toronto, Canada',
    avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=60',
    content: 'The diversity of perspectives while maintaining the core values of our faith makes every discussion enriching. Truly a blessed community.',
    rating: 5
  }
];

export function TestimonialsSection() {
  return (
    <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Blessed Testimonials
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Hear from our community members about how their spiritual journey has been enriched through our platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <Card 
              key={testimonial.id} 
              className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-muted-foreground mb-6 leading-relaxed italic">
                  "{testimonial.content}"
                </p>
                
                <div className="flex items-center space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback>{testimonial.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sacred blessing */}
        <div className="text-center mt-16">
          <div className="inline-block p-6 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900 dark:to-red-900 rounded-2xl">
            <p className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-2">
              "सत्यं शिवं सुन्दरम्"
            </p>
            <p className="text-sm text-orange-600 dark:text-orange-300">
              Truth, Goodness, Beauty
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}