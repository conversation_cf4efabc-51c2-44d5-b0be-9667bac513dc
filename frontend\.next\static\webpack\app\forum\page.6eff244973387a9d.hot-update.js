"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/posts-feed.tsx":
/*!***********************************!*\
  !*** ./components/posts-feed.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsFeed: function() { return /* binding */ PostsFeed; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _post_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./post-card */ \"(app-pages-browser)/./components/post-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ PostsFeed auto */ \n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\n// Helper function to transform Directus post to component format\nconst transformPost = (directusPost)=>{\n    var _directusPost_Categories_, _directusPost_Categories;\n    const user = typeof directusPost.user === \"object\" ? directusPost.user : null;\n    const userName = user ? \"\".concat(user.first_name || \"\", \" \").concat(user.last_name || \"\").trim() : \"Anonymous User\";\n    const firstName = (user === null || user === void 0 ? void 0 : user.first_name) || \"Anonymous\";\n    var _directusPost_Is_Public;\n    return {\n        id: directusPost.id.toString(),\n        title: directusPost.Title,\n        content: directusPost.Description,\n        author: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || \"anonymous\",\n            name: userName,\n            avatar: (user === null || user === void 0 ? void 0 : user.avatar) ? getAvatarUrl(user.avatar) : null,\n            avatarFallback: generateDummyAvatar(firstName),\n            role: \"Community Member\"\n        },\n        category: ((_directusPost_Categories = directusPost.Categories) === null || _directusPost_Categories === void 0 ? void 0 : (_directusPost_Categories_ = _directusPost_Categories[0]) === null || _directusPost_Categories_ === void 0 ? void 0 : _directusPost_Categories_.Category) || \"General\",\n        tags: directusPost.Tags || [],\n        createdAt: directusPost.date_created || new Date().toISOString(),\n        likesCount: 0,\n        commentsCount: 0,\n        isLiked: false,\n        isPinned: false,\n        isPublic: (_directusPost_Is_Public = directusPost.Is_Public) !== null && _directusPost_Is_Public !== void 0 ? _directusPost_Is_Public : true\n    };\n};\nfunction PostsFeed() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: mockPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_post_card__WEBPACK_IMPORTED_MODULE_2__.PostCard, {\n                post: post\n            }, post.id, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = PostsFeed;\nvar _c;\n$RefreshReg$(_c, \"PostsFeed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcG9zdHMtZmVlZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRW1EO0FBQ1o7QUFHdkMsNkRBQTZEO0FBQzdELE1BQU1FLHNCQUFzQixDQUFDQztRQUNaQTtJQUFmLE1BQU1DLFNBQVNELENBQUFBLHNCQUFBQSxpQ0FBQUEsb0JBQUFBLFVBQVdFLE1BQU0sQ0FBQyxnQkFBbEJGLHdDQUFBQSxrQkFBc0JHLFdBQVcsT0FBTTtJQUN0RCxNQUFNQyxTQUFTO1FBQ2I7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsYUFBYUosT0FBT0ssVUFBVSxDQUFDLEtBQUtGLE9BQU9HLE1BQU07SUFDdkQsTUFBTUMsZ0JBQWdCSixNQUFNLENBQUNDLFdBQVc7SUFFeEMscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVcsd0JBQXNDLE9BQWRGLGVBQWM7a0JBQ25EUDs7Ozs7O0FBR1A7QUFFQSxrREFBa0Q7QUFDbEQsTUFBTVUsZUFBZSxDQUFDQztJQUNwQixNQUFNQyxjQUFjQyx1QkFBb0MsSUFBSTtJQUM1RCxPQUFPLEdBQXlCRixPQUF0QkMsYUFBWSxZQUFtQixPQUFURDtBQUNsQztBQUVBLGlFQUFpRTtBQUNqRSxNQUFNSyxnQkFBZ0IsQ0FBQ0M7UUFnQlRBLDJCQUFBQTtJQWZaLE1BQU1DLE9BQU8sT0FBT0QsYUFBYUMsSUFBSSxLQUFLLFdBQVdELGFBQWFDLElBQUksR0FBRztJQUN6RSxNQUFNQyxXQUFXRCxPQUFPLEdBQTRCQSxPQUF6QkEsS0FBS0UsVUFBVSxJQUFJLElBQUcsS0FBd0IsT0FBckJGLEtBQUtHLFNBQVMsSUFBSSxJQUFLQyxJQUFJLEtBQUs7SUFDcEYsTUFBTXZCLFlBQVltQixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1FLFVBQVUsS0FBSTtRQW9CMUJIO0lBbEJaLE9BQU87UUFDTE0sSUFBSU4sYUFBYU0sRUFBRSxDQUFDQyxRQUFRO1FBQzVCQyxPQUFPUixhQUFhUyxLQUFLO1FBQ3pCQyxTQUFTVixhQUFhVyxXQUFXO1FBQ2pDQyxRQUFRO1lBQ05OLElBQUlMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUssRUFBRSxLQUFJO1lBQ2hCTyxNQUFNWDtZQUNOWSxRQUFRYixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1hLE1BQU0sSUFBR3JCLGFBQWFRLEtBQUthLE1BQU0sSUFBSTtZQUNuREMsZ0JBQWdCbEMsb0JBQW9CQztZQUNwQ2tDLE1BQU07UUFDUjtRQUNBQyxVQUFVakIsRUFBQUEsMkJBQUFBLGFBQWFrQixVQUFVLGNBQXZCbEIsZ0RBQUFBLDRCQUFBQSx3QkFBeUIsQ0FBQyxFQUFFLGNBQTVCQSxnREFBQUEsMEJBQThCbUIsUUFBUSxLQUFJO1FBQ3BEQyxNQUFNcEIsYUFBYXFCLElBQUksSUFBSSxFQUFFO1FBQzdCQyxXQUFXdEIsYUFBYXVCLFlBQVksSUFBSSxJQUFJQyxPQUFPQyxXQUFXO1FBQzlEQyxZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFVBQVU5QixDQUFBQSwwQkFBQUEsYUFBYStCLFNBQVMsY0FBdEIvQixxQ0FBQUEsMEJBQTBCO0lBQ3RDO0FBQ0Y7QUFFTyxTQUFTZ0M7SUFDZCxxQkFDRSw4REFBQ3pDO1FBQUlDLFdBQVU7a0JBQ1p5QyxVQUFVQyxHQUFHLENBQUMsQ0FBQ0MscUJBQ2QsOERBQUN2RCxnREFBUUE7Z0JBQWV1RCxNQUFNQTtlQUFmQSxLQUFLN0IsRUFBRTs7Ozs7Ozs7OztBQUk5QjtLQVJnQjBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvcG9zdHMtZmVlZC50c3g/OThhYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUG9zdENhcmQgfSBmcm9tICcuL3Bvc3QtY2FyZCc7XG5pbXBvcnQgeyBhcGksIERpcmVjdHVzUG9zdCB9IGZyb20gJ0AvbGliL2RpcmVjdHVzJztcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdlbmVyYXRlIGR1bW15IGF2YXRhciB3aXRoIGZpcnN0IGxldHRlclxuY29uc3QgZ2VuZXJhdGVEdW1teUF2YXRhciA9IChmaXJzdE5hbWU6IHN0cmluZykgPT4ge1xuICBjb25zdCBsZXR0ZXIgPSBmaXJzdE5hbWU/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnVSc7XG4gIGNvbnN0IGNvbG9ycyA9IFtcbiAgICAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1vcmFuZ2UtNDAwIHRvLXJlZC01MDAnLFxuICAgICdiZy1ncmFkaWVudC10by1iciBmcm9tLWFtYmVyLTQwMCB0by1vcmFuZ2UtNTAwJyxcbiAgICAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yZWQtNDAwIHRvLXBpbmstNTAwJyxcbiAgICAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS15ZWxsb3ctNDAwIHRvLWFtYmVyLTUwMCcsXG4gICAgJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tcGluay00MDAgdG8tcmVkLTUwMCcsXG4gICAgJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTQwMCB0by1waW5rLTUwMCcsXG4gICAgJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS00MDAgdG8tcHVycGxlLTUwMCcsXG4gICAgJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JlZW4tNDAwIHRvLWJsdWUtNTAwJyxcbiAgXTtcblxuICBjb25zdCBjb2xvckluZGV4ID0gbGV0dGVyLmNoYXJDb2RlQXQoMCkgJSBjb2xvcnMubGVuZ3RoO1xuICBjb25zdCBncmFkaWVudENsYXNzID0gY29sb3JzW2NvbG9ySW5kZXhdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2B3LTggaC04IHJvdW5kZWQtZnVsbCAke2dyYWRpZW50Q2xhc3N9IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtc20gc2hhZG93LWxnYH0+XG4gICAgICB7bGV0dGVyfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBhdmF0YXIgVVJMIGZyb20gRGlyZWN0dXNcbmNvbnN0IGdldEF2YXRhclVybCA9IChhdmF0YXJJZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGRpcmVjdHVzVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRElSRUNUVVNfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwNTUnO1xuICByZXR1cm4gYCR7ZGlyZWN0dXNVcmx9L2Fzc2V0cy8ke2F2YXRhcklkfWA7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gdHJhbnNmb3JtIERpcmVjdHVzIHBvc3QgdG8gY29tcG9uZW50IGZvcm1hdFxuY29uc3QgdHJhbnNmb3JtUG9zdCA9IChkaXJlY3R1c1Bvc3Q6IERpcmVjdHVzUG9zdCkgPT4ge1xuICBjb25zdCB1c2VyID0gdHlwZW9mIGRpcmVjdHVzUG9zdC51c2VyID09PSAnb2JqZWN0JyA/IGRpcmVjdHVzUG9zdC51c2VyIDogbnVsbDtcbiAgY29uc3QgdXNlck5hbWUgPSB1c2VyID8gYCR7dXNlci5maXJzdF9uYW1lIHx8ICcnfSAke3VzZXIubGFzdF9uYW1lIHx8ICcnfWAudHJpbSgpIDogJ0Fub255bW91cyBVc2VyJztcbiAgY29uc3QgZmlyc3ROYW1lID0gdXNlcj8uZmlyc3RfbmFtZSB8fCAnQW5vbnltb3VzJztcblxuICByZXR1cm4ge1xuICAgIGlkOiBkaXJlY3R1c1Bvc3QuaWQudG9TdHJpbmcoKSxcbiAgICB0aXRsZTogZGlyZWN0dXNQb3N0LlRpdGxlLFxuICAgIGNvbnRlbnQ6IGRpcmVjdHVzUG9zdC5EZXNjcmlwdGlvbixcbiAgICBhdXRob3I6IHtcbiAgICAgIGlkOiB1c2VyPy5pZCB8fCAnYW5vbnltb3VzJyxcbiAgICAgIG5hbWU6IHVzZXJOYW1lLFxuICAgICAgYXZhdGFyOiB1c2VyPy5hdmF0YXIgPyBnZXRBdmF0YXJVcmwodXNlci5hdmF0YXIpIDogbnVsbCxcbiAgICAgIGF2YXRhckZhbGxiYWNrOiBnZW5lcmF0ZUR1bW15QXZhdGFyKGZpcnN0TmFtZSksXG4gICAgICByb2xlOiAnQ29tbXVuaXR5IE1lbWJlcidcbiAgICB9LFxuICAgIGNhdGVnb3J5OiBkaXJlY3R1c1Bvc3QuQ2F0ZWdvcmllcz8uWzBdPy5DYXRlZ29yeSB8fCAnR2VuZXJhbCcsXG4gICAgdGFnczogZGlyZWN0dXNQb3N0LlRhZ3MgfHwgW10sXG4gICAgY3JlYXRlZEF0OiBkaXJlY3R1c1Bvc3QuZGF0ZV9jcmVhdGVkIHx8IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICBsaWtlc0NvdW50OiAwLCAvLyBUT0RPOiBJbXBsZW1lbnQgbGlrZXMgc3lzdGVtXG4gICAgY29tbWVudHNDb3VudDogMCwgLy8gVE9ETzogR2V0IGFjdHVhbCBjb21tZW50IGNvdW50XG4gICAgaXNMaWtlZDogZmFsc2UsXG4gICAgaXNQaW5uZWQ6IGZhbHNlLFxuICAgIGlzUHVibGljOiBkaXJlY3R1c1Bvc3QuSXNfUHVibGljID8/IHRydWVcbiAgfTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBQb3N0c0ZlZWQoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHttb2NrUG9zdHMubWFwKChwb3N0KSA9PiAoXG4gICAgICAgIDxQb3N0Q2FyZCBrZXk9e3Bvc3QuaWR9IHBvc3Q9e3Bvc3R9IC8+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJQb3N0Q2FyZCIsImdlbmVyYXRlRHVtbXlBdmF0YXIiLCJmaXJzdE5hbWUiLCJsZXR0ZXIiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImNvbG9ycyIsImNvbG9ySW5kZXgiLCJjaGFyQ29kZUF0IiwibGVuZ3RoIiwiZ3JhZGllbnRDbGFzcyIsImRpdiIsImNsYXNzTmFtZSIsImdldEF2YXRhclVybCIsImF2YXRhcklkIiwiZGlyZWN0dXNVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRElSRUNUVVNfVVJMIiwidHJhbnNmb3JtUG9zdCIsImRpcmVjdHVzUG9zdCIsInVzZXIiLCJ1c2VyTmFtZSIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJ0cmltIiwiaWQiLCJ0b1N0cmluZyIsInRpdGxlIiwiVGl0bGUiLCJjb250ZW50IiwiRGVzY3JpcHRpb24iLCJhdXRob3IiLCJuYW1lIiwiYXZhdGFyIiwiYXZhdGFyRmFsbGJhY2siLCJyb2xlIiwiY2F0ZWdvcnkiLCJDYXRlZ29yaWVzIiwiQ2F0ZWdvcnkiLCJ0YWdzIiwiVGFncyIsImNyZWF0ZWRBdCIsImRhdGVfY3JlYXRlZCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImxpa2VzQ291bnQiLCJjb21tZW50c0NvdW50IiwiaXNMaWtlZCIsImlzUGlubmVkIiwiaXNQdWJsaWMiLCJJc19QdWJsaWMiLCJQb3N0c0ZlZWQiLCJtb2NrUG9zdHMiLCJtYXAiLCJwb3N0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/posts-feed.tsx\n"));

/***/ })

});