'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '@/lib/directus';

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar?: string;
  role?: {
    id: string;
    name: string;
  };
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem('directus_access_token');
        const refreshToken = localStorage.getItem('directus_refresh_token');

        if (token) {
          try {
            // Try to get current user with existing token
            const userData = await authAPI.getCurrentUser(token);
            setUser(userData);
          } catch (error) {
            // Token might be expired, try to refresh
            if (refreshToken) {
              try {
                const refreshData = await authAPI.refresh(refreshToken);
                localStorage.setItem('directus_access_token', refreshData.access_token);
                localStorage.setItem('directus_refresh_token', refreshData.refresh_token);

                // Get user data with new token
                const userData = await authAPI.getCurrentUser(refreshData.access_token);
                setUser(userData);
              } catch (refreshError) {
                // Refresh failed, clear tokens
                localStorage.removeItem('directus_access_token');
                localStorage.removeItem('directus_refresh_token');
              }
            } else {
              // No refresh token, clear access token
              localStorage.removeItem('directus_access_token');
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const authData = await authAPI.login(email, password);

      // Store tokens
      localStorage.setItem('directus_access_token', authData.access_token);
      localStorage.setItem('directus_refresh_token', authData.refresh_token);

      // Get user data
      const userData = await authAPI.getCurrentUser(authData.access_token);
      setUser(userData);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
  }) => {
    setIsLoading(true);
    try {
      await authAPI.register(userData);
      // After successful registration, automatically log in
      await login(userData.email, userData.password);
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  };

  const logout = async () => {
    const refreshToken = localStorage.getItem('directus_refresh_token');

    if (refreshToken) {
      try {
        await authAPI.logout(refreshToken);
      } catch (error) {
        console.error('Logout error:', error);
      }
    }

    setUser(null);
    localStorage.removeItem('directus_access_token');
    localStorage.removeItem('directus_refresh_token');
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      login,
      register,
      logout,
      isLoading,
      isAuthenticated
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}