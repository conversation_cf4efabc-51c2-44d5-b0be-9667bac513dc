'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Home,
  TrendingUp,
  Users,
  Tag,
  BookOpen,
  HelpCircle,
  Zap,
  Code,
  Palette,
  Briefcase,
  Coffee,
  Heart,
  MessageCircle,
  Star,
  Globe
} from 'lucide-react';
import { api, DirectusCategory } from '@/lib/directus';
import { CreatePostModal } from './create-post-modal';
import { useAuth } from './auth-provider';

const navigation = [
  { name: 'Home', icon: Home, href: '/', active: true }
];

// Icon mapping for different category types
const categoryIconMap: Record<string, any> = {
  'general': BookOpen,
  'prayer': Heart,
  'discussion': MessageCircle,
  'spiritual': Star,
  'community': Users,
  'tech': Code,
  'design': Palette,
  'career': Briefcase,
  'help': HelpCircle,
  'random': Coffee,
  'global': Globe,
};

// Color mapping for categories
const categoryColors = [
  'bg-blue-500',
  'bg-orange-500',
  'bg-green-500',
  'bg-purple-500',
  'bg-red-500',
  'bg-pink-500',
  'bg-amber-500',
  'bg-indigo-500',
  'bg-teal-500',
  'bg-cyan-500',
];

// Function to get icon for category
const getCategoryIcon = (categoryName: string) => {
  const key = categoryName.toLowerCase();
  return categoryIconMap[key] || BookOpen; // Default to BookOpen
};

// Function to get color for category
const getCategoryColor = (index: number) => {
  return categoryColors[index % categoryColors.length];
};

export function Sidebar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated } = useAuth();
  const [categories, setCategories] = useState<DirectusCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await api.getCategoriesWithCounts('published', isAuthenticated);
        if (response && response.data) {
          setCategories(response.data);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [isAuthenticated]); // Re-fetch when authentication status changes

  // Update selected category from URL params
  useEffect(() => {
    const category = searchParams.get('category');
    setSelectedCategoryId(category);
  }, [searchParams]);

  const handleCategoryClick = (categoryId: string, categoryName: string) => {
    const url = `/forum?category=${categoryId}&categoryName=${encodeURIComponent(categoryName)}`;
    router.push(url);
  };

  return (
    <aside className="fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block">
      <ScrollArea className="h-full px-3 py-4">
        <div className="space-y-6">
          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Navigation
            </h3>
            <div className="space-y-1">
              {navigation.map((item) => (
                <Button
                  key={item.name}
                  variant={item.active ? 'secondary' : 'ghost'}
                  className="w-full justify-start"
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Categories
            </h3>
            <div className="space-y-1">
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
                </div>
              ) : error ? (
                <p className="text-xs text-red-500 px-3">{error}</p>
              ) : categories.length === 0 ? (
                <p className="text-xs text-muted-foreground px-3">No categories found</p>
              ) : (
                categories.map((category, index) => {
                  const IconComponent = getCategoryIcon(category.Category || '');
                  const color = getCategoryColor(index);
                  const postCount = (category as any).postCount || 0; // Use the new postCount field
                  const isSelected = selectedCategoryId === category.id.toString();

                  return (
                    <Button
                      key={category.id}
                      variant={isSelected ? "secondary" : "ghost"}
                      className="w-full justify-between"
                      onClick={() => handleCategoryClick(category.id.toString(), category.Category)}
                    >
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-3 ${color}`} />
                        <IconComponent className="mr-2 h-4 w-4" />
                        {category.Category}
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {postCount}
                      </Badge>
                    </Button>
                  );
                })
              )}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Quick Actions
            </h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={() => setShowCreatePost(true)}
                disabled={!isAuthenticated}
              >
                <Zap className="mr-2 h-4 w-4" />
                Create Post
              </Button>
              {!isAuthenticated && (
                <p className="text-xs text-muted-foreground px-3 mt-1">
                  Sign in to create posts
                </p>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>

      {/* Create Post Modal */}
      <CreatePostModal
        open={showCreatePost}
        onOpenChange={setShowCreatePost}
        onPostCreated={() => {
          // Optionally refresh the page or emit an event
          window.location.reload();
        }}
      />
    </aside>
  );
}