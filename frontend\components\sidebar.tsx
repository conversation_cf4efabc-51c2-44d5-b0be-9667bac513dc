'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Home,
  TrendingUp,
  Users,
  Tag,
  BookOpen,
  HelpCircle,
  Zap,
  Code,
  Palette,
  Briefcase,
  Coffee
} from 'lucide-react';

const navigation = [
  { name: 'Home', icon: Home, href: '/', active: true },
  { name: 'Trending', icon: TrendingUp, href: '/trending' },
  { name: 'Following', icon: Users, href: '/following' },
];

const categories = [
  { name: 'General', icon: BookOpen, count: 45, color: 'bg-blue-500' },
  { name: 'Tech Talk', icon: Code, count: 23, color: 'bg-green-500' },
  { name: 'Design', icon: Palette, count: 18, color: 'bg-purple-500' },
  { name: 'Career', icon: Briefcase, count: 12, color: 'bg-orange-500' },
  { name: 'Random', icon: Coffee, count: 8, color: 'bg-pink-500' },
  { name: 'Help & Support', icon: HelpCircle, count: 15, color: 'bg-red-500' },
];

export function Sidebar() {
  return (
    <aside className="fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block">
      <ScrollArea className="h-full px-3 py-4">
        <div className="space-y-6">
          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Navigation
            </h3>
            <div className="space-y-1">
              {navigation.map((item) => (
                <Button
                  key={item.name}
                  variant={item.active ? 'secondary' : 'ghost'}
                  className="w-full justify-start"
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Categories
            </h3>
            <div className="space-y-1">
              {categories.map((category) => (
                <Button
                  key={category.name}
                  variant="ghost"
                  className="w-full justify-between"
                >
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-3 ${category.color}`} />
                    <category.icon className="mr-2 h-4 w-4" />
                    {category.name}
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider">
              Quick Actions
            </h3>
            <div className="space-y-1">
              <Button variant="ghost" className="w-full justify-start">
                <Zap className="mr-2 h-4 w-4" />
                Create Post
              </Button>
              <Button variant="ghost" className="w-full justify-start">
                <Tag className="mr-2 h-4 w-4" />
                Browse Tags
              </Button>
            </div>
          </div>
        </div>
      </ScrollArea>
    </aside>
  );
}