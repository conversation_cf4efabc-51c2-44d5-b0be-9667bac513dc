"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/auth-provider.tsx":
/*!**************************************!*\
  !*** ./components/auth-provider.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"directus_access_token\");\n                const refreshToken = localStorage.getItem(\"directus_refresh_token\");\n                if (token) {\n                    try {\n                        // Try to get current user with existing token\n                        const userData = await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.getCurrentUser(token);\n                        setUser(userData);\n                    } catch (error) {\n                        // Token might be expired, try to refresh\n                        if (refreshToken) {\n                            try {\n                                const refreshData = await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.refresh(refreshToken);\n                                localStorage.setItem(\"directus_access_token\", refreshData.access_token);\n                                localStorage.setItem(\"directus_refresh_token\", refreshData.refresh_token);\n                                // Get user data with new token\n                                const userData = await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.getCurrentUser(refreshData.access_token);\n                                setUser(userData);\n                            } catch (refreshError) {\n                                // Refresh failed, clear tokens\n                                localStorage.removeItem(\"directus_access_token\");\n                                localStorage.removeItem(\"directus_refresh_token\");\n                            }\n                        } else {\n                            // No refresh token, clear access token\n                            localStorage.removeItem(\"directus_access_token\");\n                        }\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            const authData = await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.login(email, password);\n            // Store tokens\n            localStorage.setItem(\"directus_access_token\", authData.access_token);\n            localStorage.setItem(\"directus_refresh_token\", authData.refresh_token);\n            // Get user data\n            const userData = await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.getCurrentUser(authData.access_token);\n            setUser(userData);\n        } catch (error) {\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.register(userData);\n            // After successful registration, automatically log in\n            await login(userData.email, userData.password);\n        } catch (error) {\n            setIsLoading(false);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        const refreshToken = localStorage.getItem(\"directus_refresh_token\");\n        if (refreshToken) {\n            try {\n                await _lib_directus__WEBPACK_IMPORTED_MODULE_2__.authAPI.logout(refreshToken);\n            } catch (error) {\n                console.error(\"Logout error:\", error);\n            }\n        }\n        setUser(null);\n        localStorage.removeItem(\"directus_access_token\");\n        localStorage.removeItem(\"directus_refresh_token\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth-provider.tsx\n"));

/***/ })

});