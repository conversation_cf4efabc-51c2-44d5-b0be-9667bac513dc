"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,User,Settings,LogOut,Moon,Sun,Menu,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* harmony import */ var _login_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    var _user_first_name_, _user_first_name;\n    _s();\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { user, logout } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto flex h-16 items-center px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"ॐ\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-xl bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                                        children: \"Sacred Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 mx-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-md\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                href: \"/forum\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"hidden sm:flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Forum Discussions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"destructive\",\n                                                className: \"absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"relative h-8 w-8 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                        className: \"h-8 w-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                src: user.avatar,\n                                                                alt: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                children: ((_user_first_name = user.first_name) === null || _user_first_name === void 0 ? void 0 : (_user_first_name_ = _user_first_name[0]) === null || _user_first_name_ === void 0 ? void 0 : _user_first_name_.toUpperCase()) || user.email[0].toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                                className: \"w-56\",\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-start gap-2 p-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col space-y-1 leading-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        user.first_name,\n                                                                        \" \",\n                                                                        user.last_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                    lineNumber: 97,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Profile\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                        onClick: logout,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_User_Settings_LogOut_Moon_Sun_Menu_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Log out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>setShowLoginDialog(true),\n                                className: \"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_dialog__WEBPACK_IMPORTED_MODULE_8__.LoginDialog, {\n                open: showLoginDialog,\n                onOpenChange: setShowLoginDialog\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\header.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"mpCVeD3x/wH9BfbXT1O8l0ncBQE=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/header.tsx\n"));

/***/ })

});