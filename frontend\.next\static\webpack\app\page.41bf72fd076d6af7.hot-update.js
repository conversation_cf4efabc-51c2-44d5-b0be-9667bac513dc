"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing/features-section.tsx":
/*!*************************************************!*\
  !*** ./components/landing/features-section.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturesSection: function() { return /* binding */ FeaturesSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,BookOpen,Users,Calendar,Heart,Lightbulb,Music,Globe,Star,Zap,Shield,Target,Award,Compass,Flame,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* __next_internal_client_entry_do_not_use__ FeaturesSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Icon mapping for Material Design icons to Lucide React icons\nconst iconMap = {\n    // Chat/Communication icons\n    \"chat\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    \"message\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    \"forum\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    // Book/Study icons\n    \"menu_book\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    \"book\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    \"library_books\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    \"auto_stories\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    // User/People icons\n    \"supervised_user_circle\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    \"people\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    \"group\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    \"person\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    // Calendar/Event icons\n    \"date_range\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    \"event\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    \"schedule\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    \"calendar_today\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    // Heart/Prayer icons\n    \"favorite\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    \"volunteer_activism\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    \"healing\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    // Wisdom/Light icons\n    \"lightbulb\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    \"tips_and_updates\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    \"psychology\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    // Music icons\n    \"music_note\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    \"library_music\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    \"queue_music\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    // Global/Community icons\n    \"public\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"language\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"travel_explore\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    // Additional spiritual icons\n    \"star\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"auto_awesome\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"flash_on\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"security\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"gps_fixed\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"emoji_events\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"explore\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"local_fire_department\": _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n};\n// Gradient colors array for cycling through\nconst gradientColors = [\n    \"from-orange-500 to-red-500\",\n    \"from-amber-500 to-orange-500\",\n    \"from-red-500 to-pink-500\",\n    \"from-yellow-500 to-amber-500\",\n    \"from-pink-500 to-red-500\",\n    \"from-orange-500 to-yellow-500\",\n    \"from-purple-500 to-pink-500\",\n    \"from-blue-500 to-purple-500\",\n    \"from-green-500 to-blue-500\",\n    \"from-indigo-500 to-purple-500\"\n];\n// Function to get icon component from icon name\nconst getIconComponent = (iconName)=>{\n    return iconMap[iconName] || _barrel_optimize_names_MessageCircle_BookOpen_Users_Calendar_Heart_Lightbulb_Music_Globe_Star_Zap_Shield_Target_Award_Compass_Flame_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]; // Default to Star if icon not found\n};\n// Function to get gradient for index\nconst getGradient = (index)=>{\n    return gradientColors[index % gradientColors.length];\n};\nfunction FeaturesSection() {\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFeatures = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_3__.api.getFeatures(\"published\");\n                if (response && response.data) {\n                    setFeatures(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching features:\", err);\n                setError(\"Failed to load features\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchFeatures();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                                children: \"Spiritual Features\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Discover tools and spaces designed to nurture your spiritual growth and connect you with a community of like-minded souls\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                            children: \"Spiritual Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-red-500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\",\n                            children: \"Spiritual Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Discover tools and spaces designed to nurture your spiritual growth and connect you with a community of like-minded souls\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: features.map((feature, index)=>{\n                        const IconComponent = getIconComponent(feature.icon || \"\");\n                        const gradient = getGradient(index);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 dark:bg-black/20 backdrop-blur-sm hover:scale-105\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"text-center pb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r \".concat(gradient, \" flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg font-semibold text-foreground group-hover:text-orange-600 transition-colors\",\n                                            children: feature.title || \"Feature\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground leading-relaxed\",\n                                        children: feature.description || \"No description available\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center mt-16 space-x-8 opacity-30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-4xl text-orange-500\",\n                            children: \"\\uD83D\\uDD49️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-4xl text-red-500\",\n                            children: \"\\uD83E\\uDEB7\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-4xl text-amber-500\",\n                            children: \"\\uD83D\\uDD31\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-4xl text-pink-500\",\n                            children: \"\\uD83E\\uDE94\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\landing\\\\features-section.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesSection, \"aQ7cpY/PI6i+nBe2mOyAT3lHmy8=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing/features-section.tsx\n"));

/***/ })

});