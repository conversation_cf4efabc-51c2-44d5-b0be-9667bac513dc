'use client';

import React from 'react';
import { PostCard } from './post-card';

// Mock data for posts
const mockPosts = [
  {
    id: '1',
    title: 'Welcome to our Community Forum!',
    content: 'We\'re excited to launch this new community platform where you can connect, share ideas, and learn from each other. Feel free to introduce yourself!',
    author: {
      id: '1',
      name: 'Admin',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Admin'
    },
    category: 'General',
    tags: ['welcome', 'community'],
    createdAt: '2024-01-15T10:30:00Z',
    likesCount: 15,
    commentsCount: 8,
    isLiked: false,
    isPinned: true
  },
  {
    id: '2',
    title: 'Best practices for React development in 2024',
    content: 'What are your favorite React patterns and libraries you\'re using this year? I\'ve been exploring React Server Components and loving the performance benefits.',
    author: {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Developer'
    },
    category: 'Tech Talk',
    tags: ['react', 'development', 'best-practices'],
    createdAt: '2024-01-15T09:15:00Z',
    likesCount: 23,
    commentsCount: 12,
    isLiked: true,
    isPinned: false
  },
  {
    id: '3',
    title: 'Design system implementation tips',
    content: 'Just finished implementing a design system for our team. Here are some lessons learned and tools that made the process smoother.',
    author: {
      id: '3',
      name: 'Alex Rodriguez',
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Designer'
    },
    category: 'Design',
    tags: ['design-system', 'ui', 'workflow'],
    createdAt: '2024-01-15T08:45:00Z',
    likesCount: 18,
    commentsCount: 6,
    isLiked: false,
    isPinned: false
  },
  {
    id: '4',
    title: 'Career transition: From backend to full-stack',
    content: 'After 5 years of backend development, I\'m making the transition to full-stack. Any advice on the best way to approach learning frontend technologies?',
    author: {
      id: '4',
      name: 'Mike Johnson',
      avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Developer'
    },
    category: 'Career',
    tags: ['career', 'full-stack', 'advice'],
    createdAt: '2024-01-15T07:20:00Z',
    likesCount: 31,
    commentsCount: 24,
    isLiked: true,
    isPinned: false
  }
];

export function PostsFeed() {
  return (
    <div className="space-y-6">
      {mockPosts.map((post) => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  );
}