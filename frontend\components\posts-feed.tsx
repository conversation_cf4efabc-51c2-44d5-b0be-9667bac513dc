'use client';

import React, { useState, useEffect } from 'react';
import { PostCard } from './post-card';
import { api, DirectusPost } from '@/lib/directus';

// Helper function to generate dummy avatar with first letter
const generateDummyAvatar = (firstName: string) => {
  const letter = firstName?.charAt(0)?.toUpperCase() || 'U';
  const colors = [
    'bg-gradient-to-br from-orange-400 to-red-500',
    'bg-gradient-to-br from-amber-400 to-orange-500',
    'bg-gradient-to-br from-red-400 to-pink-500',
    'bg-gradient-to-br from-yellow-400 to-amber-500',
    'bg-gradient-to-br from-pink-400 to-red-500',
    'bg-gradient-to-br from-purple-400 to-pink-500',
    'bg-gradient-to-br from-blue-400 to-purple-500',
    'bg-gradient-to-br from-green-400 to-blue-500',
  ];

  const colorIndex = letter.charCodeAt(0) % colors.length;
  const gradientClass = colors[colorIndex];

  return (
    <div className={`w-8 h-8 rounded-full ${gradientClass} flex items-center justify-center text-white font-bold text-sm shadow-lg`}>
      {letter}
    </div>
  );
};

// Helper function to get avatar URL from Directus
const getAvatarUrl = (avatarId: string) => {
  const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
  return `${directusUrl}/assets/${avatarId}`;
};

// Helper function to transform Directus post to component format
const transformPost = (directusPost: DirectusPost) => {
  const user = typeof directusPost.user === 'object' ? directusPost.user : null;
  const userName = user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() : 'Anonymous User';
  const firstName = user?.first_name || 'Anonymous';

  return {
    id: directusPost.id.toString(),
    title: directusPost.Title,
    content: directusPost.Description,
    author: {
      id: user?.id || 'anonymous',
      name: userName,
      avatar: user?.avatar ? getAvatarUrl(user.avatar) : null,
      avatarFallback: generateDummyAvatar(firstName),
      role: 'Community Member'
    },
    category: directusPost.Categories?.[0]?.Category || 'General',
    tags: directusPost.Tags || [],
    createdAt: directusPost.date_created || new Date().toISOString(),
    likesCount: 0, // TODO: Implement likes system
    commentsCount: 0, // TODO: Get actual comment count
    isLiked: false,
    isPinned: false,
    isPublic: directusPost.Is_Public ?? true
  };
};

export function PostsFeed() {
  const [posts, setPosts] = useState<DirectusPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const response = await api.getPosts(20, 0, 'published');
        if (response && response.data) {
          setPosts(response.data);
        }
      } catch (err) {
        console.error('Error fetching posts:', err);
        setError('Failed to load posts');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-lg border p-6 animate-pulse">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-24"></div>
                <div className="h-3 bg-gray-300 rounded w-16"></div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="h-6 bg-gray-300 rounded w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded w-full"></div>
              <div className="h-4 bg-gray-300 rounded w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 dark:text-red-400 underline hover:no-underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
        <p className="text-gray-600 dark:text-gray-400 mb-2">No posts found</p>
        <p className="text-sm text-gray-500 dark:text-gray-500">Be the first to create a post!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {posts.map((directusPost) => {
        const transformedPost = transformPost(directusPost);
        return <PostCard key={transformedPost.id} post={transformedPost} />;
      })}
    </div>
  );
}