import { Header } from '@/components/header';
import { Sidebar } from '@/components/sidebar';
import { PostsFeed } from '@/components/posts-feed';
import { FloatingActionButton } from '@/components/floating-action-button';

export default function ForumPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 lg:ml-64">
          <div className="container mx-auto px-4 py-6 max-w-4xl">
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-foreground mb-2">Community Forum</h1>
              <p className="text-muted-foreground">Connect, share, and discuss with our community</p>
            </div>
            <PostsFeed />
          </div>
        </main>
      </div>
      <FloatingActionButton />
    </div>
  );
}