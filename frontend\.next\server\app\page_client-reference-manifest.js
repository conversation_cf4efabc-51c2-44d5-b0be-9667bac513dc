globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(ssr)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/community-section.tsx":{"*":{"id":"(ssr)/./components/landing/community-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/cta-section.tsx":{"*":{"id":"(ssr)/./components/landing/cta-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/features-section.tsx":{"*":{"id":"(ssr)/./components/landing/features-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/footer.tsx":{"*":{"id":"(ssr)/./components/landing/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/hero-section.tsx":{"*":{"id":"(ssr)/./components/landing/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing/testimonials-section.tsx":{"*":{"id":"(ssr)/./components/landing/testimonials-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pwa-install.tsx":{"*":{"id":"(ssr)/./app/pwa-install.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth-provider.tsx":{"*":{"id":"(ssr)/./components/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sonner.tsx":{"*":{"id":"(ssr)/./components/ui/sonner.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\header.tsx":{"id":"(app-pages-browser)/./components/header.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\community-section.tsx":{"id":"(app-pages-browser)/./components/landing/community-section.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\cta-section.tsx":{"id":"(app-pages-browser)/./components/landing/cta-section.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\features-section.tsx":{"id":"(app-pages-browser)/./components/landing/features-section.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\footer.tsx":{"id":"(app-pages-browser)/./components/landing/footer.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\hero-section.tsx":{"id":"(app-pages-browser)/./components/landing/hero-section.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\landing\\testimonials-section.tsx":{"id":"(app-pages-browser)/./components/landing/testimonials-section.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"D:\\shakti\\mandir\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"D:\\shakti\\mandir\\frontend\\app\\pwa-install.tsx":{"id":"(app-pages-browser)/./app/pwa-install.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\auth-provider.tsx":{"id":"(app-pages-browser)/./components/auth-provider.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"D:\\shakti\\mandir\\frontend\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./components/ui/sonner.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"D:\\shakti\\mandir\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false}},"entryCSSFiles":{"D:\\shakti\\mandir\\frontend\\app\\page":[],"D:\\shakti\\mandir\\frontend\\app\\layout":["static/css/app/layout.css"]}}